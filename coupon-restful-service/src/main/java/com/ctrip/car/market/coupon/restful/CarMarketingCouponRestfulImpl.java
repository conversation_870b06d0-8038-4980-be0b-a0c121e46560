package com.ctrip.car.market.coupon.restful;

import com.ctrip.basebiz.accounts.mobile.request.filter.WithAccountsMobileRequestFilter;
import com.ctrip.basebiz.accounts.mobile.request.filter.bean.AuthenticationModeEnum;
import com.ctrip.car.market.common.log.Tiger;
import com.ctrip.car.market.coupon.restful.business.*;
import com.ctrip.car.market.coupon.restful.business.seoservice.*;
import com.ctrip.car.market.coupon.restful.business.tripCarCrossRecommend.TripCarCrossRecommendBusiness;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.contract.piao.QueryQunarCouponPopupRequestType;
import com.ctrip.car.market.coupon.restful.contract.piao.QueryQunarCouponPopupResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.*;
import com.ctrip.car.market.coupon.restful.service.OfferOrderRestService;
import com.ctrip.car.market.coupon.restful.service.ProductPackageServiceV2;
import com.ctrip.soa.car.openapi.carmarketingcouponrestful.v1.CarMarketingCouponRestfulService;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CarMarketingCouponRestfulImpl implements CarMarketingCouponRestfulService {

    @Autowired
    public ProductPackageServiceV2 productPackageService;

    @Autowired
    public RedirectUrlBusiness redirectUrlBusiness;

    @Autowired
    public CouponBusiness couponBusiness;

    @Autowired
    public CornerSignBusiness cornerSignBusiness;

    @Autowired
    private QueryQualityServicesInfoBusiness queryQualityServicesInfoBusiness;

    @Autowired
    private OfferOrderRestService offerOrderRestService;


    @Autowired
    private SkinBusiness skinBusiness;

    @Autowired
    private ProductBusiness productBusiness;

    @Autowired
    private ZoneBusiness zoneBusiness;

    @Autowired
    private SeoHotLocationBusiness seoHotLocationBusiness;

    @Autowired
    private SeoHomepageDevInfoBusiness seoHomepageDevInfoBusiness;

    @Autowired
    private TripCarCrossRecommendBusiness tripCarCrossRecommendBusiness;

    @Autowired
    private QunarCouponBusiness qunarCouponBusiness;

    @Autowired
    private QunarCouponPopupBusiness qunarCouponPopupBusiness;

    @Autowired
    private UserLableCodeBusiness userLableCodeBusiness;

    @Autowired
    private MktMainProcessBusiness mktMainProcessBusiness;

    @Autowired
    private MktMainPageInfoBusiness mktMainPageInfoBusiness;

    @Autowired
    private SeoPoiDetailService seoPoiDetailService;

    @Autowired
    private SeoCommentListService seoCommentListService;

    @Autowired
    private SeoFaqService seoFaqService;

    @Autowired
    private SeoVehicleService seoVehicleService;

    @Autowired
    private SeoInformationService seoInformationService;

    @Autowired
    private SeoRecommendCityService seoRecommendCityService;

    @Autowired
    private SeoRecommendAirportService seoRecommendAirportService;

    @Autowired
    private SeoVendorScoreService seoVendorScoreService;

    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType request) {
        return new CheckHealthResponseType();
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryCarProductsResponseType queryCarProducts(QueryCarProductsRequestType request) {
        return productPackageService.queryCarProducts(request);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "receiveImposingCoupon", processDesc = "receiveImposingCoupon")
    public ReceiveImposingCouponResponseType receiveImposingCoupon(ReceiveImposingCouponRequestType request) {
        return couponBusiness.receiveImposingCoupon(request);
    }

    @Override
    @Tiger(actionName = "tripCarCrossRecommend", processDesc = "tripCarCrossRecommend")
    public TripCarCrossRecommendResponseType tripCarCrossRecommend(TripCarCrossRecommendRequestType tripCarCrossRecommendRequestType) throws Exception {
        return tripCarCrossRecommendBusiness.tripCarCrossRecommend(tripCarCrossRecommendRequestType);
    }


    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "queryCoupon", processDesc = "queryCoupon")
    public QueryCouponResponseType queryCoupon(QueryCouponRequestType request) {
        return couponBusiness.queryCoupon(request);
    }


    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "receiveCouponsByGroupID", processDesc = "receiveCouponsByGroupID")
    public ReceiveCouponsByGroupIDResponseType receiveCouponsByGroupID(ReceiveCouponsByGroupIDRequestType request) {
        return couponBusiness.receiveCouponsByGroupID(request);
    }


    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "cOMInitRCByGroupID", processDesc = "cOMInitRCByGroupID")
    public COMInitRCByGroupIDResponseType cOMInitRCByGroupID(COMInitRCByGroupIDRequestType request) {
        return couponBusiness.cOMInitRCByGroupID(request);
    }


    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryHomeIndexConfigResponseType queryHomeIndexConfig(QueryHomeIndexConfigRequestType request) {
        return cornerSignBusiness.queryHomeIndexConfig(request);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryQualityServicesInfoResponseType queryQualityServicesInfo(QueryQualityServicesInfoRequestType request) throws Exception {
        return queryQualityServicesInfoBusiness.queryQualityServicesInfo(request);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.BanH5Request)
    public QueryLabelListResponseType queryLabelList(QueryLabelListRequestType request) throws Exception {
        return queryQualityServicesInfoBusiness.queryLabelList(request);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.BanH5Request)
    @Tiger(actionName = "collectCouponCodeByUid", processDesc = "collectCouponCodeByUid")
    public CollectCouponCodeByUidResponseType collectCouponCodeByUid(CollectCouponCodeByUidRequestType request) throws Exception {
        return couponBusiness.collectCouponCodeByUid(request);
    }

    @Override
    public GetJumpUrlByPromotionIdResponseType getJumpUrlByPromotionId(GetJumpUrlByPromotionIdRequestType getJumpUrlByPromotionIdRequestType) throws Exception {
        return null;// no use
    }

    @Override
    @Tiger(actionName = "offerOrderOAuthCheck", processDesc = "offerOrderOAuthCheck")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public OfferOrderOAuthCheckResponseType offerOrderOAuthCheck(OfferOrderOAuthCheckRequestType requestType) throws Exception {
        return offerOrderRestService.offerOrderOAuthCheck(requestType);
    }

    @Override
    @Tiger(actionName = "offerOrderMember", processDesc = "offerOrderMember")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public OfferOrderMemberResponseType offerOrderMember(OfferOrderMemberRequestType offerOrderRestMemberRequestType) throws Exception {

        return offerOrderRestService.offerOrderMember(offerOrderRestMemberRequestType);
    }


    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryCouponByConfigResponseType queryCouponByConfig(QueryCouponByConfigRequestType request) throws Exception {
        return couponBusiness.queryCouponByConfig(request);
    }


    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySkins", processDesc = "querySkins")
    public QuerySkinsResponseType querySkins(QuerySkinsRequestType request) throws Exception {
        return skinBusiness.querySkins(request);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "queryZones", processDesc = "queryZones")
    public QueryZonesResponseType queryZones(QueryZonesRequestType request) throws Exception {
        return zoneBusiness.queryZones(request);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "queryTripSeoProducts", processDesc = "queryTripSeoProducts")
    public QueryTripSeoProductsResponseType queryTripSeoProducts(QueryTripSeoProductsRequestType request) throws Exception {
        return productBusiness.queryTripSeoProducts(request);
    }

    @Override
    public BaseResponse base(BaseRequest baseRequest) throws Exception {
        return null;
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySeoHotLocation", processDesc = "querySeoHotLocation")
    public QuerySeoHotLocationResponseType querySeoHotLocation(QuerySeoHotLocationRequestType querySeoHotLocationRequestType) {
        return seoHotLocationBusiness.queryHotLocation(querySeoHotLocationRequestType);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySeoInformation", processDesc = "querySeoInformation")
    public QuerySeoInformationResponseType querySeoInformation(QuerySeoInformationRequestType querySeoInformationRequestType) {
        return seoInformationService.queryInformation(querySeoInformationRequestType);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySeoPoiDetail", processDesc = "querySeoPoiDetail")
    public QuerySeoPoiDetailResponseType querySeoPoiDetail(QuerySeoPoiDetailRequestType querySeoPoiDetailRequestType) {
        return seoPoiDetailService.queryPoiDetail(querySeoPoiDetailRequestType);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySeoVehicleList", processDesc = "querySeoVehicleList")
    public QuerySeoVehicleListResponseType querySeoVehicleList(QuerySeoVehicleListRequestType querySeoVehicleListRequestType) {
        return seoVehicleService.queryVehicleList(querySeoVehicleListRequestType);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySeoFaq", processDesc = "querySeoFaq")
    public QuerySeoFaqResponseType querySeoFaq(QuerySeoFaqRequestType querySeoFaqRequestType) throws Exception {
        return seoFaqService.queryFaq(querySeoFaqRequestType);
    }

    @Override
    @Tiger(actionName = "getTripHomepageDevInfo", processDesc = "getTripHomepageDevInfo")
    public GetTripHomepageDevInfoResponseType getTripHomepageDevInfo(GetTripHomepageDevInfoRequestType getTripHomepageDevInfoRequestType) throws Exception {
        return seoHomepageDevInfoBusiness.getTripHomepageDevInfo(getTripHomepageDevInfoRequestType);
    }

    @Override
    @Tiger(actionName = "querySeoCommentList", processDesc = "querySeoCommentList")
    public QuerySeoCommentListResponseType querySeoCommentList(QuerySeoCommentListRequestType querySeoCommentListRequestType) throws Exception {
        return seoCommentListService.queryCommentList(querySeoCommentListRequestType);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "querySeoRecommendAirport", processDesc = "querySeoRecommendAirport")
    public QuerySeoRecommendAirportResponseType querySeoRecommendAirport(QuerySeoRecommendAirportRequestType querySeoRecommendAirportRequestType) throws Exception {
        return seoRecommendAirportService.queryRecommendAirport(querySeoRecommendAirportRequestType);
    }

    @Override
    @Tiger(actionName = "querySeoRecommendCity", processDesc = "querySeoRecommendCity")
    public QuerySeoRecommendCityResponseType querySeoRecommendCity(QuerySeoRecommendCityRequestType querySeoRecommendCityRequestType) throws Exception {
        return seoRecommendCityService.queryRecommendCity(querySeoRecommendCityRequestType);
    }

    @Override
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    @Tiger(actionName = "queryQunarCoupon", processDesc = "queryQunarCoupon")
    public QueryQunarCouponResponseType queryQunarCoupon(QueryQunarCouponRequestType queryQunarCouponRequestType) throws Exception {
        return qunarCouponBusiness.queryQunarCoupon(queryQunarCouponRequestType);
    }

    @Override
    @Tiger(actionName = "queryQunarCouponPopup", processDesc = "queryQunarCouponPopup")
    public QueryQunarCouponPopupResponseType queryQunarCouponPopup(QueryQunarCouponPopupRequestType queryQunarCouponPopupRequestType) throws Exception {
        return qunarCouponPopupBusiness.queryPopup(queryQunarCouponPopupRequestType);
    }


    @Override
    @Tiger(actionName = "queryUserMktCondition", processDesc = "queryUserMktCondition")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public queryUserMktConditionResponseType queryUserMktCondition(queryUserMktConditionRequestType queryUserMktConditionRequestType) throws Exception {
        return userLableCodeBusiness.queryUserMktCondition(queryUserMktConditionRequestType);

    }



    @Override
    @Tiger(actionName = "queryUserLabelCode", processDesc = "queryUserLabelCode")
    public queryUserLabelCodeResponseType queryUserLabelCode(queryUserLabelCodeRequestType queryUserLabelCodeRequestType) throws Exception {
        return userLableCodeBusiness.queryUserLabelCode(queryUserLabelCodeRequestType);
    }

    @Override
    @Tiger(actionName = "queryMktMainPageInfo", processDesc = "queryMktMainPageInfo")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryMktMainPageInfoResponseType queryMktMainPageInfo(QueryMktMainPageInfoRequestType queryMktMainPageInfoRequestType) throws Exception {
        return mktMainPageInfoBusiness.queryMktMainPageInfo(queryMktMainPageInfoRequestType);
    }


    @Override
    @Tiger(actionName = "queryMktMainProcessInfo", processDesc = "queryMktMainProcessInfo")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryMktMainProcessInfoResponseType queryMktMainProcessInfo(QueryMktMainProcessInfoRequestType queryMktMainProcessInfoRequestType) throws Exception {
        return mktMainProcessBusiness.queryMktMainProcessInfo(queryMktMainProcessInfoRequestType);
    }

    @Autowired
    private MktSupportDetailsBusiness mktSupportDetailsBusiness;

    @Override
    @Tiger(actionName = "queryMktSupportDetails", processDesc = "queryMktSupportDetails")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QueryMktSupportDetailsResponseType queryMktSupportDetails(QueryMktSupportDetailsRequestType queryMktSupportDetailsRequestType) throws Exception {
        return mktSupportDetailsBusiness.getMktSupportDetails(queryMktSupportDetailsRequestType);
    }

    @Override
    @Tiger(actionName = "querySeoVendorScore", processDesc = "querySeoVendorScore")
    @WithAccountsMobileRequestFilter(authenticationMode = AuthenticationModeEnum.OnDemand)
    public QuerySeoVendorScoreResponseType querySeoVendorScore(QuerySeoVendorScoreRequestType querySeoVendorScoreRequestType) throws Exception {
        return seoVendorScoreService.queryVendorScore(querySeoVendorScoreRequestType);
    }
}


