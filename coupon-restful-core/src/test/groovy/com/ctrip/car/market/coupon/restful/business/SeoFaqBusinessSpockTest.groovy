package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.business.seoservice.faq.SeoCityFaqBusiness
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoCityVehicleBusiness
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.FaqInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType
import com.ctrip.car.market.coupon.restful.dto.KeyValueDto
import com.ctrip.car.market.coupon.restful.dto.LocaleSortDto
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang.StringUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification


class SeoFaqBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def vehicleBusiness = Mock(SeoCityVehicleBusiness)

    def tripConfig = Mock(TripConfig)

    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def testInstance = new SeoCityFaqBusiness(
            service: service,
            vehicleBusiness: vehicleBusiness,
            tripConfig: tripConfig,
            osdShoppingProxy: osdShoppingProxy
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def "test checkRequest"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QuerySeoFaqRequestType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as QuerySeoFaqRequestType)) == result

        where:
        request                                                               || result
        new QuerySeoFaqRequestType()                                          || false
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest())            || false
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(), cityId: 1) || true
    }

    def "test queryProduct"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("queryProduct", BaseRequest.class, MobileRequestHead.class, SeoHotDestinatioinfoDO.class)
        testMethod.setAccessible(true)

        given:
        vehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as SeoHotDestinatioinfoDO, _ as Boolean) >> response

        expect:
        ((List<RecomdProductRes>) testMethod.invoke(testInstance, new BaseRequest(), new MobileRequestHead(), new SeoHotDestinatioinfoDO())).isEmpty() == result

        where:
        response                                                                            || result
        new QueryRecomdProductsResponseType()                                               || true
        new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes()]) || false
    }

    def "test getFaqSort"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getFaqSort", int.class, String.class)
        testMethod.setAccessible(true)

        given:
        tripConfig.getLocaleSortList() >> [new LocaleSortDto(type: 2, locale: "en-US", faqSort: [1, 2, 3])]

        expect:
        ((List<Integer>) testMethod.invoke(testInstance, 2, locale as String)).isEmpty() == result

        where:
        locale  || result
        "zh-HK" || false
        "en-US" || false
    }

    def "test buildFaq"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("buildFaq", String.class, String.class)
        testMethod.setAccessible(true)

        expect:
        Objects.nonNull(((FaqInfo) testMethod.invoke(testInstance, q as String, a as String))) == result

        where:
        q      | a      || result
        null   | null   || false
        "test" | "test" || true
    }

    def "test getVehicleGroupFaq"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getVehicleGroupFaq", String.class, String.class, String.class)
        testMethod.setAccessible(true)

        given:
        tripConfig.getVehicleGroupMappingList() >> mapping
        vehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as SeoHotDestinatioinfoDO, _ as Boolean) >> null


        expect:
        StringUtils.isNotEmpty(((String) testMethod.invoke(testInstance, vehicleGroupCode as String, "test", "en-US"))) == result

        where:
        mapping                                             | vehicleGroupCode || result
        [new KeyValueDto(key: "", value: "")]               | ""               || false
        [new KeyValueDto(key: "", value: "")]               | "small"          || false
        [new KeyValueDto(key: "small", value: "small")]     | "small"          || true
        [new KeyValueDto(key: "medium", value: "medium")]   | "medium"         || true
        [new KeyValueDto(key: "premium", value: "premium")] | "premium"        || true
        [new KeyValueDto(key: "suv", value: "suv")]         | "suv"            || true
        [new KeyValueDto(key: "van", value: "van")]         | "van"            || true
        [new KeyValueDto(key: "coupe", value: "coupe")]     | "coupe"          || true
        [new KeyValueDto(key: "pickup", value: "pickup")]   | "pickup"         || true
        [new KeyValueDto(key: "high", value: "high")]       | "high"           || true
        [new KeyValueDto(key: "small", value: "small")]     | "test"           || false
    }

//    def "test getFaq"() {
//        def testMethod = testInstance.getClass().getDeclaredMethod("getFaq", SeoHotDestinatioinfoDO.class, SeoHotInformationDO.class, QuerySeoFaqRequestType.class, QuerySeoFaqResponseType.class)
//        testMethod.setAccessible(true)
//
//        given:
//        vehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as SeoHotDestinatioinfoDO, _ as Boolean) >> new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(
//                vehicle: new RecomdVehicleDTO(groupCode: "test"), price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.TEN)
//        )])])
//        tripConfig.getVehicleGroupMappingList() >> [new KeyValueDto(key: "small", value: "small")]
//        service.getCityName(_ as Integer, _ as String) >> "test"
//        service.queryHotCityByCity(_ as Integer) >> new SeoHotCityinfoDO(url: "test")
//        service.getAirportName(_ as String, _ as String) >> "test"
//        service.getSiteUrl(_ as String, _ as String) >> "test"
//        tripConfig.getLocaleSortList() >> [new LocaleSortDto(type: 2, locale: "en-US", faqSort: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 38, 42, 43]),
//                                           new LocaleSortDto(type: 3, locale: "en-US", faqSort: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 38, 42, 43])]
//
//        expect:
//        ((List<FaqInfo>) testMethod.invoke(testInstance, destinatioinfo as SeoHotDestinatioinfoDO, information as SeoHotInformationDO, req as QuerySeoFaqRequestType, new QuerySeoFaqResponseType())).isEmpty() == result
//
//        where:
//        destinatioinfo                        | information                                                    | req                                                                                                   || result
//        new SeoHotDestinatioinfoDO(cityId: 1) | new SeoHotInformationDO(cityId: 1, poiType: 1, poiCode: "LAX") | new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), cityId: 1)                  || false
//        new SeoHotDestinatioinfoDO(cityId: 1) | new SeoHotInformationDO(cityId: 1, poiType: 1, poiCode: "LAX") | new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), poiType: 1, poiCode: "LAX") || false
//    }

    def "test getPoiName"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getPoiName", QuerySeoFaqRequestType.class)
        testMethod.setAccessible(true)

        given:
        service.getAirportName(_ as String, _ as String) >> "test"
        service.getCityName(_ as Integer, _ as String) >> "test"
        service.getCountryName(_ as Integer, _ as String) >> "test"

        expect:
        (String) testMethod.invoke(testInstance, request as QuerySeoFaqRequestType) == result

        where:
        request                                                                                                              || result
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1L)                             || "test"
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1L, cityId: 1L)                 || "test"
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1L, cityId: 1L, poiCode: "AAA") || "test"
        new QuerySeoFaqRequestType()                                                                                         || null
    }

    def "test getWebSite"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getWebSite", QuerySeoFaqRequestType.class)
        testMethod.setAccessible(true)

        given:
        service.queryHotDestinationFirst(null, null, 1, _ as String) >> hotDestinatioinfoDO
        service.queryHotCityByCity(_ as Integer) >> hotCityinfoDO
        service.queryHotCountryByCountry(_ as Integer) >> hotCountryinfoDO
        service.getSiteUrl(_ as String, _ as String) >> "test"

        expect:
        (String) testMethod.invoke(testInstance, request as QuerySeoFaqRequestType) == result

        where:
        request                                                                                               | hotDestinatioinfoDO                     | hotCityinfoDO                     | hotCountryinfoDO                     || result
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"), poiType: 1, poiCode: "AAA") | null                                    | null                              | null                                 || null
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"), poiType: 1, poiCode: "AAA") | new SeoHotDestinatioinfoDO(url: "test") | null                              | null                                 || "test"

        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"), cityId: 1)                  | null                                    | null                              | null                                 || null
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"), cityId: 1)                  | null                                    | new SeoHotCityinfoDO(url: "test") | null                                 || "test"

        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"), countryId: 1)               | null                                    | null                              | null                                 || null
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"), countryId: 1)               | null                                    | null                              | new SeoHotCountryinfoDO(url: "test") || "test"

        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-Us"))                             | null                                    | null                              | null                                 || null
    }

    def "test queryFaq"() {
        given:
        service.queryHotDestinationFirst(_ as Integer, _ as Integer, _ as Integer, _ as String) >> hotDestinationInfo
        service.queryCityDefaultPoi(_ as String, _ as Integer, _ as String) >> globalInfo
        tripConfig.getVehicleGroupMappingList() >> []
        tripConfig.getLocaleSortList() >> []
        osdShoppingProxy.getCarCard(_ as BaseRequest, _ as String,"") >> false

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryFaq(request as QuerySeoFaqRequestType).getFaqList()) == result

        where:
        request                                                                                           | hotDestinationInfo | globalInfo                           || result
        new QuerySeoFaqRequestType()                                                                      | null               | null                                 || false
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"))                         | null               | null                                 || false
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), cityId: 1)              | null               | null                                 || false
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "en-US"), cityId: 1, poiCode: "") | null               | new PlaceDetailsDTO(carPlaceId: "1") || false
    }
}
