package com.ctrip.car.market.coupon.restful.business.seoservice.comment

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.BaseResponse
import com.ctrip.car.market.coupon.restful.contract.seo.CommentInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListResponseType
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.enums.SeoShark
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.ListCommentsResponseType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CommentDetailInfoType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CtripUserInfoType
import org.apache.commons.collections4.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoProvinceCommentBusinessTest extends Specification {

    def seoService = Mock(SeoService)
    def ucpServiceProxy = Mock(UcpServiceProxy)

    def testInstance = new SeoProvinceCommentBusiness(
            seoService: seoService,
            ucpServiceProxy: ucpServiceProxy
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic
    MockedStatic<LanguageUtils> languageUtilsMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        languageUtilsMockedStatic = Mockito.mockStatic(LanguageUtils.class)
        
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
        languageUtilsMockedStatic.when { LanguageUtils.sharkValFormat(_ as String, _ as String) }.thenReturn("Test Title")
        languageUtilsMockedStatic.when { LanguageUtils.sharkValFormat(_ as String, _ as String, _ as String) }.thenReturn("Test Title")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
        languageUtilsMockedStatic.close()
    }

    def "test constructor"() {
        when:
        def business = new SeoProvinceCommentBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.COUNTRY)
    }

    def "test doBusiness with null queryParameter"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        seoService.buildQueryParameter(1, null, null) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        seoService.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.getMessage() == "para error"
    }

    def "test doBusiness with empty locale"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> new ListCommentsResponseType(comments: [])

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.commentList.isEmpty()
    }

    def "test doBusiness with null baseRequest"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> new ListCommentsResponseType(comments: [])

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.commentList.isEmpty()
    }

    def "test doBusiness with null commentsResponse"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> null
        seoService.getProvinceName(1L, "en-US") >> "Test Province"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.totalCount == 0
        result.commentList == null
        result.baseResponse.getMessage() == "success"
    }

    def "test doBusiness with valid comments for PROVINCE page"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def commentDetail = createCommentDetail()
        def commentsResponse = new ListCommentsResponseType(
                comments: [commentDetail],
                totalCount: 1
        )
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> commentsResponse
        seoService.commentDateFormat(commentDetail.commentTime, "en-US") >> "2023-01-01"
        seoService.getProvinceName(1L, "en-US") >> "Test Province"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.totalCount == 1
        result.commentList != null
        result.commentList.size() == 1
        
        def comment = result.commentList[0]
        comment.avatarUrl == "http://avatar.url"
        comment.userDisplayName == "Test User"
        comment.content == "Great service!"
        comment.translatedContent == "Excellent service!"
        comment.socre == 4.5
        comment.commentTime == "2023-01-01"
        comment.vehicleName == "Toyota Camry"

        result.baseResponse.getMessage() == "success"
    }

    def "test doBusiness with valid comments for STATION page"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def commentDetail = createCommentDetail()
        def commentsResponse = new ListCommentsResponseType(
                comments: [commentDetail],
                totalCount: 1
        )
        
        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> commentsResponse
        seoService.commentDateFormat(commentDetail.commentTime, "en-US") >> "2023-01-01"
        seoService.getGlobalPoiName(1L, "en-US") >> "Test Station"

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.totalCount == 1
        result.commentList != null
        result.commentList.size() == 1
        result.baseResponse.getMessage() == "success"
    }

    def "test doBusiness with valid comments for SCENERY page"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def commentDetail = createCommentDetail()
        def commentsResponse = new ListCommentsResponseType(
                comments: [commentDetail],
                totalCount: 1
        )
        
        seoService.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> commentsResponse
        seoService.commentDateFormat(commentDetail.commentTime, "en-US") >> "2023-01-01"
        seoService.queryPoiName(2, "1", "en-US") >> "Test Scenery"

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.totalCount == 1
        result.commentList != null
        result.commentList.size() == 1
        result.baseResponse.getMessage() == "success"
    }

    def "test doBusiness with empty extInfoMap"() {
        given:
        def request = new QuerySeoCommentListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def commentDetail = new CommentDetailInfoType(
                userInfo: new CtripUserInfoType(
                        avatarUrl: "http://avatar.url",
                        displayName: "Test User"
                ),
                content: "Great service!",
                translatedContent: "Excellent service!",
                score: 4.5,
                commentTime: System.currentTimeMillis(),
                extInfoMap: null
        )
        def commentsResponse = new ListCommentsResponseType(
                comments: [commentDetail],
                totalCount: 1
        )
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        ucpServiceProxy.queryComment("en-US", null, 123) >> commentsResponse
        seoService.commentDateFormat(commentDetail.commentTime, "en-US") >> "2023-01-01"
        seoService.getProvinceName(1L, "en-US") >> "Test Province"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.commentList != null
        result.commentList.size() == 1
        result.commentList[0].vehicleName == null
    }

    private SeoProvinceQueryParameter createValidQueryParameter(SeoPage seoPage) {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(seoPage)
        
        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(123)
        
        def poiA = new SeoPoiA()
        poiA.setPoiId(1L)
        configItem.setPoiA(poiA)
        
        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private CommentDetailInfoType createCommentDetail() {
        return new CommentDetailInfoType(
                userInfo: new CtripUserInfoType(
                        avatarUrl: "http://avatar.url",
                        displayName: "Test User"
                ),
                content: "Great service!",
                translatedContent: "Excellent service!",
                score: 4.5,
                commentTime: System.currentTimeMillis(),
                extInfoMap: ["vehicleName": "Toyota Camry"]
        )
    }
}
