package com.ctrip.car.market.coupon.restful.business.seovendor

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoVendorVehicleBusiness
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListRequestType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO
import com.ctrip.car.osd.shopping.api.entity.LocationRequestInfo
import com.ctrip.car.osd.shopping.api.entity.OsdLabelInfo
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsRequestType
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification

class SeoVendorVehicleBusinessTest extends Specification {

    def seoVendorCache = Mock(SeoVendorCache)

    def service = Mock(SeoService)

    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoVendorVehicleBusiness(
            seoVendorCache: seoVendorCache,
            service: service,
            osdShoppingProxy: osdShoppingProxy,
            tripConfig: tripConfig
    )

    def test() {
        given:
        seoVendorCache.queryVendorCity(_ as String, _ as Integer) >> vendorCityDO
        seoVendorCache.queryVendor(_ as String) >> vendorDO
        service.queryVendorCity(_ as String, _ as Integer) >> new SeoHotVendorInformationDO(vendorCode: "SD0001", vendorName: "test", cityId: 1, poiType: 1, poiCode: "test")
        service.queryVendorTop3City(_ as String) >> [new SeoHotVendorInformationDO(vendorCode: "SD0001", vendorName: "test", cityId: 1, poiType: 1, poiCode: "test")]
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> getVehicleResponse()
        tripConfig.getMaxVehicleCount() >> 9
        service.currencyString(_ as BigDecimal, _ as String, _ as String) >> "test"
        tripConfig.getPcUrl() >> "test"

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryVehicleList(request as QuerySeoVehicleListRequestType).getCityVehicleList()) == result

        where:
        request                                                                                       | vendorCityDO             | vendorDO                                                  || result
        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD")
                , head: new MobileRequestHead(), vendorCode: "SD0001")                                | null                     | null                                                       | false

        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD")
                , head: new MobileRequestHead(), vendorCode: "SD0001")                                | null                     | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") | true

        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD")
                , head: new MobileRequestHead(), vendorCode: "SD0001", cityId: 1)                     | null                     | null                                                       | false

        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD")
                , head: new MobileRequestHead(), vendorCode: "SD0001", cityId: 1)                     | new SeoHotVendorCityDO() | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") | true

    }

    def test2() {
        given:
        seoVendorCache.queryVendorCity(_ as String, _ as Integer) >> vendorCityDO
        seoVendorCache.queryVendor(_ as String) >> vendorDO
        service.queryVendorCity(_ as String, _ as Integer) >> null
        service.queryVendorTop3City(_ as String) >> [null]
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> getVehicleResponse()
        tripConfig.getMaxVehicleCount() >> 9
        service.currencyString(_ as BigDecimal, _ as String, _ as String) >> "test"
        tripConfig.getPcUrl() >> "test"

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryVehicleList(request as QuerySeoVehicleListRequestType).getCityVehicleList()) == result

        where:
        request                                                                                       | vendorCityDO             | vendorDO                                                  || result
        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD")
                , head: new MobileRequestHead(), vendorCode: "SD0001")                                | null                     | null                                                       | false

        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD")
                , head: new MobileRequestHead(), vendorCode: "SD0001")                                | null                     | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") | false

    }

    def getVehicleResponse() {
        return new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(
                pickupLocation: new LocationRequestInfo(cityId: 1, cityName: "test"),
                products: [new RecomdProduct(vehicle: new RecomdVehicleDTO(vehicleKey: "test", vehicleCode: "1", groupCode: "1"), labels: [new OsdLabelInfo(code: "test"), new OsdLabelInfo(code: "discount")],
                        price: new RecomdPriceDTO(currentOriginalDailyPrice: BigDecimal.TWO, currentDailyPrice: BigDecimal.ONE))]
        )], sourceCountryId: 1)
    }
}
