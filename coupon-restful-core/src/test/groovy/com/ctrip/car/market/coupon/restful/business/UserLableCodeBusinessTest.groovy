package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.common.utils.ABTestUtils
import com.ctrip.car.market.coupon.restful.cache.SeoCityCache
import com.ctrip.car.market.coupon.restful.contract.JumpImageInfo
import com.ctrip.car.market.coupon.restful.contract.RestRequestHeader
import com.ctrip.car.market.coupon.restful.contract.UserLabelInfo
import com.ctrip.car.market.coupon.restful.contract.queryUserLabelCodeRequestType
import com.ctrip.car.market.coupon.restful.contract.queryUserMktConditionRequestType
import com.ctrip.car.market.coupon.restful.contract.queryUserMktConditionResponseType
import com.ctrip.car.market.coupon.restful.dto.UserMktConditionDTO
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy
import com.ctrip.car.market.coupon.restful.utils.BaseUtils
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig
import com.ctrip.car.market.coupon.restful.utils.JsonUtils
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import credis.java.client.util.JsonUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import soa.ctrip.com.tour.ai.user.label.LabelInfo
import spock.lang.*

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class UserLableCodeBusinessTest extends Specification {

    def userLabelAiProxy = Mock(UserLabelAiProxy)
    def serviceQConfig = Mock(CouponServiceQConfig)
    def userLableCodeBusiness = new UserLableCodeBusiness(userLabelAiProxy: userLabelAiProxy, serviceQConfig: serviceQConfig)
    List<UserMktConditionDTO> list = new ArrayList<>();
    MockedStatic<BaseUtils> baseUtilsMockedStatic
    MockedStatic<ABTestUtils> abTestUtilsMockedStatic

    def setup() {
        serviceQConfig.getUserMktConditions() >> list
        serviceQConfig.getUserMktConditions2() >> list
        serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber") >> "shareABTestNumber";
        serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber") >> "wechatBTestNumber"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.wechat") >> "ISD_C_APP"

        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.app") >> "ISD_C_WX"
        baseUtilsMockedStatic = Mockito.mockStatic(BaseUtils.class)
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("test")

        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("test")

        abTestUtilsMockedStatic = Mockito.mockStatic(ABTestUtils.class)
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB(Mockito.anyString(), Mockito.anyString()))
                .thenReturn("AAAAA")

    }

    def cleanup() {
        baseUtilsMockedStatic.close()
        abTestUtilsMockedStatic.close()

    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])


        expect:
        userLableCodeBusiness.queryUserLabelCode(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || true
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 1) || true

    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult   nologin"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn(null)

        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn(null)

        expect:
        userLableCodeBusiness.queryUserLabelCode(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || null
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 1) || null

    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult   noResult"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> null

        expect:
        userLableCodeBusiness.queryUserLabelCode(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || Boolean.FALSE
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 1) || Boolean.FALSE
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), null, 1)           || null

    }

    @Unroll
    def "query User Mkt Condition where request=#request then expect: #expectedResult"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode", sourceFrom: "ISD_C_APP", abResult: "23", display: "34434")])

        expect:
        userLableCodeBusiness.queryUserMktCondition(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                                              || expectedResult
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "sceneCode", "ISD_C_APP", 0, ["labelId"], 0) || true
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "sceneCode", "ISD_C_APP", 0, ["labelId"], 1) || true
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "sceneCode", "ISD_C_APP", 0, ["labelId"], 2) || true
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), null, "sceneCode", "ISD_C_APP", 0, ["labelId"], 2)   || true

    }

    def "buildResultNew 返回 JumpImageInfo 列表，当条件匹配时"() {
        given: "构造一个匹配条件的 UserMktConditionDTO 对象"
        serviceQConfig.getUserMktConditions3() >> [new UserMktConditionDTO(
                sceneCode: "middleBanner",
                sourceFrom: "ISD_C_APP",
                shareTestingVersion: "B",  // 参数 v1 在此列表中
                wechatTestingVersion: "B",  // 参数 w1 在此列表中
                newOrOldVersion: "N",         // 参数 N 在此列表中
                miniType: 0,
                useABTest: true,
                imageUrl: "http://image.com",
                jumpUrl: "http://jump.com",
                display: "5"
        )]

        when: "通过反射调用私有方法 buildResultNew"
        def method = UserLableCodeBusiness.getDeclaredMethod("buildResultNew", String, String, String, String, String, Integer)
        method.setAccessible(true)
        def result = method.invoke(userLableCodeBusiness, "ISD_C_APP", "middleBanner", "B", "B", "N", 0)

        then: "返回的列表大小为 1，且各属性与 dto 对应"
        result instanceof List
        def jumpInfo = result[0] as JumpImageInfo
        jumpInfo.imageUrl == "http://image.com"
        jumpInfo.jumpUrl == "http://jump.com"
        jumpInfo.displayFrequency == 5L
    }

    def "buildResultNew 返回空列表，当条件不匹配时"() {
        given: "构造一个条件不匹配的 UserMktConditionDTO 对象"
        def userMktCondition = new UserMktConditionDTO(
                sceneCode: "sceneCode1",
                sourceFrom: "testSource",
                shareTestingVersion: "v2",      // 参数 v1 不在此列表中
                wechatTestingVersion: "w2",      // 参数 w1 不在此列表中
                newOrOldVersion: "O",            // 参数 N 不在此列表中
                miniType: 1,
                useABTest: true,
                imageUrl: "http://image.com",
                jumpUrl: "http://jump.com",
                display: "5"
        )
        serviceQConfig.getUserMktConditions3() >> [userMktCondition]

        when: "通过反射调用私有方法 buildResultNew，参数与 dto 中的不匹配"
        def method = UserLableCodeBusiness.getDeclaredMethod("buildResultNew", String, String, String, String, String, Integer)
        method.setAccessible(true)
        def result = method.invoke(userLableCodeBusiness, "testSource", "sceneCode1", "v1", "w1", "N", 1)

        then: "返回空列表"
        result instanceof List
    }
}

