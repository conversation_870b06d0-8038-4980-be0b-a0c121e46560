package com.ctrip.car.market.coupon.restful.business.seoservice.vendorscore

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVendorScoreRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVendorScoreResponseType
import com.ctrip.car.market.coupon.restful.contract.seo.VendorScoreItem
import com.ctrip.car.market.coupon.restful.dto.CustomerConfigDTO
import com.ctrip.car.market.coupon.restful.dto.SeoHotVendorDTO
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.dto.SubItemConfigDto
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO
import com.ctrip.car.osd.framework.common.utils.JsonUtil
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.CommonQueryCommentSummaryResponseType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CommentAggregationInfoType
import com.google.common.collect.Lists
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

import java.math.BigDecimal

class SeoVendorScoreBusinessTest extends Specification {

    def seoService = Mock(SeoService)
    def tripConfig = Mock(TripConfig)
    def seoVendorCache = Mock(SeoVendorCache)
    def ucpServiceProxy = Mock(UcpServiceProxy)
    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def testInstance = new SeoVendorScoreBusiness(
            seoService: seoService,
            tripConfig: tripConfig,
            seoVendorCache: seoVendorCache,
            ucpServiceProxy: ucpServiceProxy,
            osdShoppingProxy: osdShoppingProxy
    )

    MockedStatic<JsonUtil> jsonUtilMockedStatic

    def setup() {
        jsonUtilMockedStatic = Mockito.mockStatic(JsonUtil.class)
    }

    def cleanup() {
        jsonUtilMockedStatic.close()
    }

    def "test constructor"() {
        when:
        def business = new SeoVendorScoreBusiness()

        then:
        business.isSupport(SeoPage.TRIP_HOME)
        business.isSupport(SeoPage.COUNTRY)
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.CITY)
        business.isSupport(SeoPage.AIRPORT)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.VENDOR)
    }

    def "test doBusiness for COUNTRY page"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                countryId: 1,
                cityId: 123,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def hotVendorConfig = createHotVendorConfig()
        def vendorList = ["HERTZ", "AVIS"]
        
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(vendorList)
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.COUNTRY)

        then:
        result != null
        result.vendorScoreList != null
        result.vendorScoreList.size() > 0
    }

    def "test doBusiness for PROVINCE page"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                provinceId: 5,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def queryParameter = createValidQueryParameter()
        def hotVendorConfig = createHotVendorConfig()
        
        seoService.buildQueryParameter(5, null, null) >> queryParameter
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness for STATION page"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def queryParameter = createValidQueryParameter()
        def hotVendorConfig = createHotVendorConfig()
        
        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness for SCENERY page"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def queryParameter = createValidQueryParameter()
        def hotVendorConfig = createHotVendorConfig()
        
        seoService.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness for AIRPORT page"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                poiCode: "LAX",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def destination = createDestination()
        def hotVendorConfig = createHotVendorConfig()
        
        seoService.queryHotDestinationFirst(null, null, 1, "LAX") >> destination
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.AIRPORT)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness for CITY page"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                countryId: 1,
                cityId: 123,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def hotVendorConfig = createHotVendorConfig()
        
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.CITY)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness with null queryParameter for PROVINCE"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                provinceId: 5,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def hotVendorConfig = createHotVendorConfig()
        
        seoService.buildQueryParameter(5, null, null) >> null
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness with null destination for AIRPORT"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                poiCode: "LAX",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def hotVendorConfig = createHotVendorConfig()
        
        seoService.queryHotDestinationFirst(null, null, 1, "LAX") >> null
        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.AIRPORT)

        then:
        result != null
        result.vendorScoreList != null
    }

    def "test doBusiness with null hotVendorConfig"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                countryId: 1,
                cityId: 123,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        
        tripConfig.getSeoHotVendor() >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.COUNTRY)

        then:
        result != null
        result.vendorScoreList != null
        result.vendorScoreList.isEmpty()
    }

    def "test doBusiness with null vendor in cache"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                countryId: 1,
                cityId: 123,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def hotVendorConfig = createHotVendorConfig()

        tripConfig.getSeoHotVendor() >> hotVendorConfig
        seoVendorCache.queryVendor("HERTZ") >> null
        seoVendorCache.queryVendor("AVIS") >> createVendorDO("AVIS", "Avis")
        seoVendorCache.queryVendor("BUDGET") >> createVendorDO("BUDGET", "Budget")
        setupCityMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.COUNTRY)

        then:
        result != null
        result.vendorScoreList != null
        result.vendorScoreList.size() == 1 // Only AVIS and BUDGET, HERTZ filtered out
    }

    def "test doBusiness with exception in getVendorPrice"() {
        given:
        def request = new QuerySeoVendorScoreRequestType(
                countryId: 1,
                cityId: 123,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD")
        )
        def hotVendorConfig = createHotVendorConfig()

        tripConfig.getSeoHotVendor() >> hotVendorConfig
        setupVendorMocks(["HERTZ"])
        setupCityMocks()
        osdShoppingProxy.getVendorPrice(_, "HERTZ") >> { throw new RuntimeException("Price service error") }

        when:
        def result = testInstance.doBusiness(request, SeoPage.COUNTRY)

        then:
        result != null
        result.vendorScoreList != null
        result.vendorScoreList.size() == 1
        result.vendorScoreList[0].price == "\$50.00"
    }

    def "test getVendorScore with different locales"() {
        given:
        def baseRequest = new BaseRequest(locale: locale, currencyCode: "USD")
        def vendorList = ["HERTZ"]
        def cityId = 123

        setupVendorMocks(vendorList)
        setupCityMocks()

        when:
        def testMethod = testInstance.getClass().getDeclaredMethod("getVendorScore", List.class, BaseRequest.class, SeoPage.class, Integer.class)
        testMethod.setAccessible(true)
        def result = testMethod.invoke(testInstance, vendorList, baseRequest, SeoPage.COUNTRY, cityId) as List<VendorScoreItem>

        then:
        result != null
        result.size() == 1
        result[0].vendorCode == "HERTZ"

        where:
        locale << ["en-US", "zh-CN", "ja-JP", "ko-KR", "fr-FR"]
    }

    def "test scoreFormat method"() {
        given:
        def testMethod = testInstance.getClass().getDeclaredMethod("scoreFormat", BigDecimal.class)
        testMethod.setAccessible(true)

        when:
        def result = testMethod.invoke(testInstance, input) as BigDecimal

        then:
        result == expected

        where:
        input                           | expected
        BigDecimal.valueOf(4.56)       | BigDecimal.valueOf(4.6)
        BigDecimal.valueOf(4.51)       | BigDecimal.valueOf(4.6)
        BigDecimal.valueOf(4.50)       | BigDecimal.valueOf(4.5)
        BigDecimal.valueOf(4.49)       | BigDecimal.valueOf(4.5)
        BigDecimal.valueOf(5.0)        | BigDecimal.valueOf(5.0)
    }

    def "test getHotVendor with city config"() {
        given:
        def hotVendorConfig = createHotVendorConfigWithCityConfig()
        def testMethod = testInstance.getClass().getDeclaredMethod("getHotVendor", Integer.class, Integer.class)
        testMethod.setAccessible(true)

        tripConfig.getSeoHotVendor() >> hotVendorConfig

        when:
        def result = testMethod.invoke(testInstance, 1, 123) as List<String>

        then:
        result != null
        result == ["HERTZ", "AVIS"]
    }

    def "test getHotVendor with country config fallback"() {
        given:
        def hotVendorConfig = createHotVendorConfigWithCountryConfig()
        def testMethod = testInstance.getClass().getDeclaredMethod("getHotVendor", Integer.class, Integer.class)
        testMethod.setAccessible(true)

        tripConfig.getSeoHotVendor() >> hotVendorConfig

        when:
        def result = testMethod.invoke(testInstance, 1, 999) as List<String> // cityId 999 not in config

        then:
        result != null
        result == ["BUDGET", "ENTERPRISE"]
    }

    def "test getHotVendor with default config fallback"() {
        given:
        def hotVendorConfig = createHotVendorConfigWithDefaultOnly()
        def testMethod = testInstance.getClass().getDeclaredMethod("getHotVendor", Integer.class, Integer.class)
        testMethod.setAccessible(true)

        tripConfig.getSeoHotVendor() >> hotVendorConfig

        when:
        def result = testMethod.invoke(testInstance, 999, 999) as List<String> // Neither country nor city in config

        then:
        result != null
        result == ["ALAMO", "NATIONAL"]
    }

    def "test getVendorPrice with successful response"() {
        given:
        def baseRequest = new BaseRequest(locale: "en-US", currencyCode: "USD")
        def vendorCode = "HERTZ"
        def priceDTO = new RecomdPriceDTO()
        priceDTO.setCurrentDailyPrice(BigDecimal.valueOf(50.0))
        def testMethod = testInstance.getClass().getDeclaredMethod("getVendorPrice", BaseRequest.class, String.class)
        testMethod.setAccessible(true)

        osdShoppingProxy.getVendorPrice(baseRequest, vendorCode) >> priceDTO
        seoService.currencyString(BigDecimal.valueOf(50.0), "en-US", "USD") >> "\$50.00"

        when:
        def result = testMethod.invoke(testInstance, baseRequest, vendorCode) as String

        then:
        result == "\$50.00"
    }

    def "test getVendorPrice with null response"() {
        given:
        def baseRequest = new BaseRequest(locale: "en-US", currencyCode: "USD")
        def vendorCode = "HERTZ"
        def testMethod = testInstance.getClass().getDeclaredMethod("getVendorPrice", BaseRequest.class, String.class)
        testMethod.setAccessible(true)

        osdShoppingProxy.getVendorPrice(baseRequest, vendorCode) >> null

        when:
        def result = testMethod.invoke(testInstance, baseRequest, vendorCode) as String

        then:
        result == null
    }

    // Helper methods for creating test data
    private SeoHotVendorDTO createHotVendorConfig() {
        def config = new SeoHotVendorDTO()
        config.setDefaultConfig("HERTZ,AVIS,BUDGET")

        def customerConfigs = []
        def cityConfig = new CustomerConfigDTO()
        cityConfig.setType(2) // City type
        cityConfig.setId(123)
        cityConfig.setVendorList("HERTZ,AVIS")
        customerConfigs.add(cityConfig)

        def countryConfig = new CustomerConfigDTO()
        countryConfig.setType(1) // Country type
        countryConfig.setId(1)
        countryConfig.setVendorList("BUDGET,ENTERPRISE")
        customerConfigs.add(countryConfig)

        config.setCustomerConfig(customerConfigs)
        return config
    }

    private SeoHotVendorDTO createHotVendorConfigWithCityConfig() {
        def config = new SeoHotVendorDTO()
        config.setDefaultConfig("DEFAULT1,DEFAULT2")

        def customerConfigs = []
        def cityConfig = new CustomerConfigDTO()
        cityConfig.setType(2) // City type
        cityConfig.setId(123)
        cityConfig.setVendorList("HERTZ,AVIS")
        customerConfigs.add(cityConfig)

        config.setCustomerConfig(customerConfigs)
        return config
    }

    private SeoHotVendorDTO createHotVendorConfigWithCountryConfig() {
        def config = new SeoHotVendorDTO()
        config.setDefaultConfig("DEFAULT1,DEFAULT2")

        def customerConfigs = []
        def countryConfig = new CustomerConfigDTO()
        countryConfig.setType(1) // Country type
        countryConfig.setId(1)
        countryConfig.setVendorList("BUDGET,ENTERPRISE")
        customerConfigs.add(countryConfig)

        config.setCustomerConfig(customerConfigs)
        return config
    }

    private SeoHotVendorDTO createHotVendorConfigWithDefaultOnly() {
        def config = new SeoHotVendorDTO()
        config.setDefaultConfig("ALAMO,NATIONAL")
        config.setCustomerConfig([])
        return config
    }

    private SeoProvinceQueryParameter createValidQueryParameter() {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)

        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(123)
        configItem.setCountryId(1)

        def poiA = new SeoPoiA()
        poiA.setPoiId(2L)
        configItem.setPoiA(poiA)

        def poiB = new SeoPoiB()
        poiB.setPoiCode("LAX")
        poiB.setPoiType(1)
        configItem.setPoiB(poiB)

        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private SeoHotDestinatioinfoDO createDestination() {
        def destination = new SeoHotDestinatioinfoDO()
        destination.setCountryId(1)
        destination.setCityId(123)
        destination.setPoiCode("LAX")
        destination.setPoiType(1)
        return destination
    }

    private SeoHotVendorDO createVendorDO(String vendorCode, String vendorName) {
        def vendorDO = new SeoHotVendorDO()
        vendorDO.setVendorId(vendorCode)
        vendorDO.setVendorName(vendorName)
        vendorDO.setUrl("https://example.com/" + vendorCode.toLowerCase())
        return vendorDO
    }

    private SeoVendorCommentScoreDO createVendorCommentScoreDO(String vendorCode) {
        def scoreDO = new SeoVendorCommentScoreDO()
        scoreDO.setVendorId(vendorCode)
        scoreDO.setSocre(BigDecimal.valueOf(4.5))
        scoreDO.setTotalCount(100)
        scoreDO.setSubItemScore('[{"subItemConfigId":"cleanliness","scoreAvg":4.6},{"subItemConfigId":"service","scoreAvg":4.4}]')
        return scoreDO
    }

    private CommonQueryCommentSummaryResponseType createCommentSummaryResponse() {
        def response = new CommonQueryCommentSummaryResponseType()
        def aggregation = new CommentAggregationInfoType()
        aggregation.setScoreAvg(BigDecimal.valueOf(4.3))
        aggregation.setTotalCount(50)
        aggregation.setSubItemTags([])
        response.setCommentAggregation(aggregation)
        return response
    }

    private List<VendorScoreItem> createVendorScoreList() {
        def item1 = new VendorScoreItem()
        item1.setVendorCode("HERTZ")
        item1.setVendorName("Hertz")
        item1.setScore(BigDecimal.valueOf(4.5))
        item1.setCommentCount(100)
        item1.setPrice("\$50.00")

        def item2 = new VendorScoreItem()
        item2.setVendorCode("AVIS")
        item2.setVendorName("Avis")
        item2.setScore(BigDecimal.valueOf(4.3))
        item2.setCommentCount(80)
        item2.setPrice("\$55.00")

        return [item1, item2]
    }

    private void setupVendorMocks(List<String> vendorList) {
        vendorList.each { vendorCode ->
            seoVendorCache.queryVendor(vendorCode) >> createVendorDO(vendorCode, vendorCode.toLowerCase().capitalize())
            seoService.getVendorLogo(vendorCode) >> "https://example.com/logo/${vendorCode.toLowerCase()}.png"
            seoService.getSiteUrl(_, _) >> "https://example.com/${vendorCode.toLowerCase()}"
            osdShoppingProxy.getVendorPrice(_, vendorCode) >> new RecomdPriceDTO(currentDailyPrice: BigDecimal.valueOf(50.0))
            seoService.currencyString(_, _, _) >> "\$50.00"
        }
    }

    private void setupTripHomeMocks() {
        seoVendorCache.queryVendorCommentScore(_ as String) >> createVendorCommentScoreDO("HERTZ")
        seoVendorCache.queryVendorCommentScore("AVIS") >> createVendorCommentScoreDO("AVIS")
        seoVendorCache.queryVendorCommentScore("BUDGET") >> createVendorCommentScoreDO("BUDGET")

        def subItemList = [
                new SubItemConfigDto(subItemConfigId: "cleanliness", scoreAvg: BigDecimal.valueOf(4.6)),
                new SubItemConfigDto(subItemConfigId: "service", scoreAvg: BigDecimal.valueOf(4.4))
        ]

        jsonUtilMockedStatic.when { JsonUtil.getObjList(_, SubItemConfigDto.class) }.thenReturn(subItemList)
        seoService.dbSubItemConvert(subItemList, _) >> []
    }

    private void setupCityMocks() {
        ucpServiceProxy.queryVendorCityComment(_, _, _) >> createCommentSummaryResponse()
        seoService.apiSubItemConvert(_, _) >> []
    }
}
