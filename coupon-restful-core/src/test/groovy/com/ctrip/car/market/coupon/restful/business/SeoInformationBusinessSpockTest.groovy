package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.business.seoservice.information.SeoCityInformationBusiness
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoCityVehicleBusiness
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.InformationInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO
import com.ctrip.car.osd.shopping.api.entity.*
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoInformationBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def vehicleBusiness = Mock(SeoCityVehicleBusiness)

    def testInstance = new SeoCityInformationBusiness(
            service: service,
            vehicleBusiness: vehicleBusiness
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def "test checkRequest"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QuerySeoInformationRequestType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as QuerySeoInformationRequestType)) == result

        where:
        request                                                                       || result
        new QuerySeoInformationRequestType()                                          || false
        new QuerySeoInformationRequestType(baseRequest: new BaseRequest())            || false
        new QuerySeoInformationRequestType(baseRequest: new BaseRequest(), cityId: 1) || true
    }

    def "test getInformation"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getInformation", int.class, String.class, String.class)
        testMethod.setAccessible(true)

        expect:
        ((InformationInfo) testMethod.invoke(testInstance, type as int, "test", "en-US")).getName() == result

        where:
        type || result
        1    || "test"
        2    || "test"
        3    || "test"
        4    || "test"
    }

    def "test getVehicleGroupPrice"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getVehicleGroupPrice", SeoHotDestinatioinfoDO.class, QuerySeoInformationRequestType.class, String.class)
        testMethod.setAccessible(true)

        given:
        vehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as SeoHotDestinatioinfoDO, _ as Boolean) >> vehicleResponse

        expect:
        ((BigDecimal) testMethod.invoke(testInstance, new SeoHotDestinatioinfoDO(), new QuerySeoInformationRequestType(baseRequest: new BaseRequest(), head: new MobileRequestHead()), "test")) != null == result

        where:
        vehicleResponse                                                                                                                                                                                                                  || result
        new QueryRecomdProductsResponseType()                                                                                                                                                                                            || false
        new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "test"), price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.TEN))])]) || true
    }

    def "test getInformation2"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getInformation", SeoHotDestinatioinfoDO.class, QuerySeoInformationRequestType.class)
        testMethod.setAccessible(true)

        given:
        service.queryInformationByPoi(_ as Integer, _ as String) >> information
        service.getVehicleGroupName(_ as String, _ as String, _ as String) >> "test"
        service.currencyString(_ as BigDecimal, _ as String, _ as String) >> "test"
        vehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as SeoHotDestinatioinfoDO, _ as Boolean) >> new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "1"), price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.TEN))])])

        expect:
        ((List<InformationInfo>) testMethod.invoke(testInstance, new SeoHotDestinatioinfoDO(poiType: 1, poiCode: "test"), new QuerySeoInformationRequestType(baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"), head: new MobileRequestHead()))).isEmpty() == result

        where:
        information                                                                                          || result
        null                                                                                                 || true
        new SeoHotInformationDO(vendorName: "test", vehicleGroupId: 1, vehicleGroupName: "test", tenancy: 1) || false
        new SeoHotInformationDO(vendorName: "test", vehicleGroupId: 1, vehicleGroupName: "test", tenancy: 2) || false
    }

    def "test queryInformation"() {
        given:
        service.queryHotDestinationFirst(_ as Integer, _ as Integer, _ as Integer, _ as String) >> null

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryInformation(request as QuerySeoInformationRequestType).getInformationList()) == result

        where:
        request                                                                           || result
        new QuerySeoInformationRequestType()                                              || false
        new QuerySeoInformationRequestType(baseRequest: new BaseRequest(locale: "en-US")) || false
    }

}
