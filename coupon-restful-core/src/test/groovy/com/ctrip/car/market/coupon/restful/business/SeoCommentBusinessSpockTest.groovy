package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.business.seoservice.comment.SeoCityCommentBusiness
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListRequestType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.ListCommentsResponseType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CommentDetailInfoType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CtripUserInfoType
import org.apache.commons.collections4.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoCommentBusinessSpockTest extends Specification {

    def ucpServiceProxy = Mock(UcpServiceProxy)

    def seoService = Mock(SeoService)

    def testInstance = new SeoCityCommentBusiness(
            ucpServiceProxy: ucpServiceProxy,
            seoService: seoService
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }


    def "test queryComment"() {
        given:


        seoService.getCityName(_ as Integer, _ as String) >> "test"
        seoService.getCountryName(_ as Integer, _ as String) >> "test"
        seoService.queryHotDestinationFirst(null, null, _ as Integer, _ as String) >> destinationInfo
        ucpServiceProxy.queryComment(_ as String, _ as Integer, _ as Integer) >> commentList
        seoService.commentDateFormat(_ as Long, _ as String) >> "test"

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryComment(request as QuerySeoCommentListRequestType).getCommentList()) == result

        where:
        request                                                                                                                      | destinationInfo                       | commentList                                                                                                                                     || result
        new QuerySeoCommentListRequestType()                                                                                         | null                                  | new ListCommentsResponseType(comments: [])                                                                                                      || false
        new QuerySeoCommentListRequestType(countryId: 0, poiType: 1, poiCode: "test", baseRequest: new BaseRequest(locale: "en-US")) | null                                  | new ListCommentsResponseType(comments: [])                                                                                                      || false
        new QuerySeoCommentListRequestType(countryId: 0, poiType: 1, poiCode: "test", baseRequest: new BaseRequest(locale: "en-US")) | new SeoHotDestinatioinfoDO(cityId: 1) | new ListCommentsResponseType(comments: [])                                                                                                      || false
        new QuerySeoCommentListRequestType(countryId: 0, poiType: 1, poiCode: "test", baseRequest: new BaseRequest(locale: "en-US")) | new SeoHotDestinatioinfoDO(cityId: 1) | new ListCommentsResponseType(comments: [new CommentDetailInfoType(userInfo: new CtripUserInfoType(), commentTime: System.currentTimeMillis())]) || true
    }

}
