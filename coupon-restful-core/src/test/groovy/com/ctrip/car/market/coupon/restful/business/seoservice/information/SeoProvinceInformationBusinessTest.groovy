package com.ctrip.car.market.coupon.restful.business.seoservice.information

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.InformationInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationResponseType
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils
import spock.lang.Specification

class SeoProvinceInformationBusinessTest extends Specification {

    def seoService = Mock(SeoService)
    def seoCityInformationBusiness = Mock(SeoCityInformationBusiness)

    def testInstance = new SeoProvinceInformationBusiness(
            seoService: seoService,
            seoCityInformationBusiness: seoCityInformationBusiness
    )

    def "test constructor"() {
        when:
        def business = new SeoProvinceInformationBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.COUNTRY)
        !business.isSupport(SeoPage.VENDOR)
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoInformationRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        seoService.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }


    def "test doBusiness with seoCityInformationBusiness returning null"() {
        given:
        def request = new QuerySeoInformationRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE, 123)

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoCityInformationBusiness.queryInformation(_ as QuerySeoInformationRequestType) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result == null
    }

    def "test doBusiness with different request parameters"() {
        given:
        def queryParameter = createValidQueryParameter(seoPage, 123)
        def expectedResponse = createSuccessResponse()

        seoService.buildQueryParameter(provinceId, poiType, poiCode) >> queryParameter
        seoCityInformationBusiness.queryInformation(_ as QuerySeoInformationRequestType) >> expectedResponse

        when:
        def result = testInstance.doBusiness(request, seoPage)

        then:
        result != null
        result == expectedResponse

        where:
        request                                                                                                           | seoPage          | provinceId | poiType | poiCode
        new QuerySeoInformationRequestType(provinceId: 1, baseRequest: new BaseRequest())                                 | SeoPage.PROVINCE | 1          | null    | null
        new QuerySeoInformationRequestType(poiType: 2, poiCode: "STATION", baseRequest: new BaseRequest())                | SeoPage.STATION  | null       | 2       | "STATION"
        new QuerySeoInformationRequestType(poiType: 20, poiCode: "SCENERY", baseRequest: new BaseRequest())               | SeoPage.SCENERY  | null       | 20      | "SCENERY"
        new QuerySeoInformationRequestType(provinceId: 5, poiType: 1, poiCode: "AIRPORT", baseRequest: new BaseRequest()) | SeoPage.PROVINCE | 5          | 1       | "AIRPORT"
    }

    private SeoProvinceQueryParameter createValidQueryParameter(SeoPage seoPage, Integer cityId) {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(seoPage)

        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(cityId)

        def poiA = new SeoPoiA()
        poiA.setPoiId(1L)
        configItem.setPoiA(poiA)

        def poiB = new SeoPoiB()
        poiB.setPoiCode("LAX")
        poiB.setPoiType(1)
        configItem.setPoiB(poiB)

        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private QuerySeoInformationResponseType createSuccessResponse() {
        def response = new QuerySeoInformationResponseType()
        def informationList = Lists.newArrayList()

        def info1 = new InformationInfo()
        info1.setType(1)
        info1.setName("Test Vendor")
        info1.setTitle("Most popular car rental company")
        informationList.add(info1)

        def info2 = new InformationInfo()
        info2.setType(2)
        info2.setName("Economy")
        info2.setTitle("The most popular to book")
        informationList.add(info2)

        response.setInformationList(informationList)
        response.setBaseResponse(com.ctrip.car.market.coupon.restful.utils.ResponseUtil.success())
        return response
    }

    private QuerySeoInformationResponseType createErrorResponse() {
        def response = new QuerySeoInformationResponseType()
        response.setInformationList(Lists.newArrayList())
        response.setBaseResponse(com.ctrip.car.market.coupon.restful.utils.ResponseUtil.fail("test error"))
        return response
    }
}
