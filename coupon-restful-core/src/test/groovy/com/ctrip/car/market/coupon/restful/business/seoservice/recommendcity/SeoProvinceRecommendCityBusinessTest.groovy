package com.ctrip.car.market.coupon.restful.business.seoservice.recommendcity

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityResponseType
import com.ctrip.car.market.coupon.restful.contract.seo.RecommendCityInfo
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.apache.commons.collections4.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoProvinceRecommendCityBusinessTest extends Specification {

    def seoService = Mock(SeoService)
    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoProvinceRecommendCityBusiness(
            seoService: seoService,
            tripConfig: tripConfig
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic
    MockedStatic<LanguageUtils> languageUtilsMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        languageUtilsMockedStatic = Mockito.mockStatic(LanguageUtils.class)
        
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
        languageUtilsMockedStatic.when { LanguageUtils.sharkValFormat(_ as String, _ as Object[]) }.thenReturn("formatted test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
        languageUtilsMockedStatic.close()
    }

    def "test constructor"() {
        when:
        def business = new SeoProvinceRecommendCityBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.COUNTRY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.VENDOR)
    }

    def "test doBusiness with null queryParameter"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        seoService.buildQueryParameter(1, null, null) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        seoService.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness with empty city list"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.queryHotCity(1) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.cityList == null
        result.title == null
        result.subTitle == null
    }

    def "test doBusiness with successful response for PROVINCE"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def cityList = createCityList()
        def destinationList = createDestinationList()
        def cityOrderMap = createCityOrderMap()
        def cityMap = createCityMap()
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.queryHotCity(1) >> cityList
        seoService.queryHotDestination(1, null, null, null) >> destinationList
        seoService.getCountryName(1, "en-US") >> "United States"
        seoService.getCityOrderNum(destinationList) >> cityOrderMap
        seoService.getCity([1L, 2L, 3L], "en-US") >> cityMap
        seoService.getSiteUrl(_ as String, "en-US") >> { String url, String locale -> 
            return url?.replace("www", "us")
        }
        tripConfig.getMaxRecommendCityCount() >> 10

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.cityList != null
        result.cityList.size() > 0
    }

    def "test doBusiness with successful response for STATION"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def cityList = createCityList()
        def destinationList = createDestinationList()
        
        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        seoService.queryHotCity(1) >> cityList
        seoService.queryHotDestination(1, null, null, null) >> destinationList
        seoService.getCountryName(1, "en-US") >> "United States"
        setupConvertMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.baseResponse.message == "success"
        result.cityList != null
    }

    def "test doBusiness with successful response for SCENERY"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def cityList = createCityList()
        def destinationList = createDestinationList()
        
        seoService.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        seoService.queryHotCity(1) >> cityList
        seoService.queryHotDestination(1, null, null, null) >> destinationList
        seoService.getCountryName(1, "en-US") >> "United States"
        setupConvertMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.baseResponse.message == "success"
        result.cityList != null
    }

    def "test doBusiness with different locales"() {
        given:
        def request = new QuerySeoRecommendCityRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: locale)
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def cityList = createCityList()
        def destinationList = createDestinationList()
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.queryHotCity(1) >> cityList
        seoService.queryHotDestination(1, null, null, null) >> destinationList
        seoService.getCountryName(1, locale) >> "Test Country"
        setupConvertMocks()

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.cityList != null

        where:
        locale << ["en-US", "zh-CN", "ja-JP", "ko-KR", "fr-FR"]
    }

    private SeoProvinceQueryParameter createValidQueryParameter(SeoPage seoPage) {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(seoPage)
        
        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(123)
        configItem.setCountryId(1)
        
        def poiA = new SeoPoiA()
        poiA.setPoiId(2L)
        configItem.setPoiA(poiA)
        
        def poiB = new SeoPoiB()
        poiB.setPoiCode("LAX")
        poiB.setPoiType(1)
        configItem.setPoiB(poiB)
        
        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private List<SeoHotCityinfoDO> createCityList() {
        return [
                createCity(1, "New York", "https://example.com/ny.jpg"),
                createCity(2, "Los Angeles", "https://example.com/la.jpg"),
                createCity(3, "Chicago", "https://example.com/chicago.jpg")
        ]
    }

    private SeoHotCityinfoDO createCity(Integer cityId, String cityName, String image) {
        def city = new SeoHotCityinfoDO()
        city.setCityId(cityId)
        city.setImage(image)
        city.setUrl("https://example.com/city/$cityId")
        return city
    }

    private List<SeoHotDestinatioinfoDO> createDestinationList() {
        return [
                createDestination(1, 1, 100),
                createDestination(2, 2, 80),
                createDestination(3, 3, 60)
        ]
    }

    private SeoHotDestinatioinfoDO createDestination(Integer cityId, Integer poiType, Integer orderNum) {
        def destination = new SeoHotDestinatioinfoDO()
        destination.setCityId(cityId)
        destination.setPoiType(poiType)
        destination.setOrderNum(orderNum)
        destination.setPoiCode("AIRPORT$cityId")
        return destination
    }

    private Map<Integer, Long> createCityOrderMap() {
        def map = Maps.newHashMap()
        map.put(1, -100L)
        map.put(2, -80L)
        map.put(3, -60L)
        return map
    }

    private Map<Long, City> createCityMap() {
        def map = Maps.newHashMap()
        map.put(1L, createCityEntity(1L, "New York"))
        map.put(2L, createCityEntity(2L, "Los Angeles"))
        map.put(3L, createCityEntity(3L, "Chicago"))
        return map
    }

    private City createCityEntity(Long cityId, String name) {
        def city = Mock(City)
        city.getTranslationName() >> name
        return city
    }

    private void setupConvertMocks() {
        seoService.getCityOrderNum(_ as List) >> createCityOrderMap()
        seoService.getCity(_ as List, _ as String) >> createCityMap()
        seoService.getSiteUrl(_ as String, _ as String) >> { String url, String locale -> 
            return url?.replace("www", locale?.contains("-") ? locale.split("-")[1] : "us")
        }
        tripConfig.getMaxRecommendCityCount() >> 10
    }
}
