package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.business.seoservice.recommendairport.SeoRecommendAirportBusiness
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoCityVehicleBusiness
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendAirportRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.RecommendAirportInfo
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import org.apache.commons.collections4.CollectionUtils
import org.assertj.core.util.Sets
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoRecommendAirportBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def tripConfig = Mock(TripConfig)

    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def seoVehicleBusiness = Mock(SeoCityVehicleBusiness)

    def testInstance = new SeoRecommendAirportBusiness(
            service: service,
            tripConfig: tripConfig,
            osdShoppingProxy: osdShoppingProxy,
            seoVehicleBusiness: seoVehicleBusiness
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def "test getAirportPrice"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getAirportPrice", SeoHotDestinatioinfoDO.class, BaseRequest.class)
        testMethod.setAccessible(true)

        given:
        osdShoppingProxy.getAirportPrice(_ as BaseRequest, _ as String) >> priceDto
        seoVehicleBusiness.queryVehicle(_ as BaseRequest, null, _ as SeoHotDestinatioinfoDO, _ as Boolean) >> response

        expect:
        Objects.nonNull(((RecomdPriceDTO) testMethod.invoke(testInstance, new SeoHotDestinatioinfoDO(poiCode: "test"), new BaseRequest()))) == result

        where:
        priceDto             | response                                                                                                                                                                       || result
        new RecomdPriceDTO() | null                                                                                                                                                                           || true
        null                 | null                                                                                                                                                                           || false
        null                 | new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.TEN))])]) || true
    }

    def "test convert"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("convert", List.class, BaseRequest.class)
        testMethod.setAccessible(true)

        given:
        tripConfig.getMaxRecommendAirportCount() >> 10
        osdShoppingProxy.getAirportPrice(_ as BaseRequest, _ as String) >> new RecomdPriceDTO(currentDailyPrice: BigDecimal.TEN)
        service.getSiteUrl(_ as String, _ as String) >> "test"
        service.currencyString(_ as BigDecimal, _ as String, _ as String) >> "test"

        expect:
        CollectionUtils.isNotEmpty(((List<RecommendAirportInfo>) testMethod.invoke(testInstance, destinationList as List<SeoHotDestinatioinfoDO>, new BaseRequest(locale: "zh-HK", currencyCode: "HKD")))) == result

        where:
        destinationList                                            || result
        []                                                         || false
        [new SeoHotDestinatioinfoDO(poiCode: "test", url: "test")] || true
    }

    def "test queryAirport"() {
        given:
        tripConfig.getMaxRecommendAirportCount() >> 10
        osdShoppingProxy.getAirportPrice(_ as BaseRequest, _ as String) >> new RecomdPriceDTO(currentDailyPrice: BigDecimal.TEN)
        service.getSiteUrl(_ as String, _ as String) >> "test"
        service.currencyString(_ as BigDecimal, _ as String, _ as String) >> "test"
        tripConfig.getTwCityList() >> Sets.newHashSet()
        service.getCountryName(_ as Integer, _ as String) >> "test"
        service.queryHotDestination(_ as Integer, null, null, null) >> destinationList

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryAirport(request as QuerySeoRecommendAirportRequestType).getAirportList()) == result

        where:
        request                                                                                                                    | destinationList                                                                      || result
        new QuerySeoRecommendAirportRequestType()                                                                                  | []                                                                                   || false
        new QuerySeoRecommendAirportRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD"))                | []                                                                                   || false
        new QuerySeoRecommendAirportRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD"), countryId: 1L) | []                                                                                   || false
        new QuerySeoRecommendAirportRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD"), countryId: 1L) | [new SeoHotDestinatioinfoDO(countryId: 1L, cityId: 1L, poiCode: "test", poiType: 1)] || true
        new QuerySeoRecommendAirportRequestType(baseRequest: new BaseRequest(locale: "zh-TW", currencyCode: "HKD"), countryId: 1L) | [new SeoHotDestinatioinfoDO(countryId: 1L, cityId: 1L, poiCode: "test", poiType: 1)] || true
    }
}
