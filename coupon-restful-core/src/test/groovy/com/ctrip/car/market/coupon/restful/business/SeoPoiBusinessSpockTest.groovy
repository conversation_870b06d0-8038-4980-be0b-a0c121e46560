package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.business.seoservice.poi.SeoCityPoiDetailBusiness
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.QueryZonesRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType
import com.ctrip.car.market.coupon.restful.contract.SeoZone
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO
import spock.lang.Specification

class SeoPoiBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def carCrossRecommendedServiceProxy = Mock(CarCrossRecommendedServiceProxy)

    def seoPlatformProxy = Mock(SeoPlatformProxy)

    def testInstance = new SeoCityPoiDetailBusiness(service: service, carCrossRecommendedServiceProxy: carCrossRecommendedServiceProxy, seoPlatformProxy: seoPlatformProxy)

    def setup() {
    }

    def "test checkRequest"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QuerySeoPoiDetailRequestType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as QuerySeoPoiDetailRequestType)) == result

        where:
        request                                                                     || result
        new QuerySeoPoiDetailRequestType()                                          || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest())            || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(), cityId: 1) || true
    }

    def "test queryPoiDetail"() {
        given:
        service.queryHotDestinationFirst(_ as Integer, _ as Integer, _ as Integer, _ as String) >> hotDestinationInfo
        carCrossRecommendedServiceProxy.queryZones(_ as QueryZonesRequestType) >> zone
        seoPlatformProxy.querySeoName(_ as int, _ as String, _ as String) >> null
        service.queryCityDefaultPoi(_ as String, _ as Integer, _ as String) >> globalInfo

        expect:
        testInstance.queryPoiDetail(request as QuerySeoPoiDetailRequestType).getPoiDetail() != null == result

        where:
        request                                                                                                                               | hotDestinationInfo                                                   | globalInfo                                                                                              | zone                                            || result
        new QuerySeoPoiDetailRequestType()                                                                                                    | null                                                                 | null                                                                                                    | null                                            || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | null                                                                 | null                                                                                                    | null                                            || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | new SeoHotDestinatioinfoDO(cityId: 1, poiCode: "test", countryId: 1) | null                                                                                                    | new QueryZonesResponseType()                    || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | new SeoHotDestinatioinfoDO(cityId: 1, poiCode: "test", countryId: 1) | null                                                                                                    | new QueryZonesResponseType(zone: new SeoZone()) || true
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "en-US"), countryId: 1, cityId: 1, poiType: 1, poiCode: "")     | null                                                                 | new PlaceDetailsDTO(name: "test", carPlaceId: "1", longitude: BigDecimal.TEN, latitude: BigDecimal.TEN) | new QueryZonesResponseType(zone: new SeoZone()) || true
    }
}
