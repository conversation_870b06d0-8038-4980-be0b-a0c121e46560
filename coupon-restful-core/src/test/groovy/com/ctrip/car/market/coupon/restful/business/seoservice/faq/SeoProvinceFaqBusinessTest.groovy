package com.ctrip.car.market.coupon.restful.business.seoservice.faq

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoProvinceVehicleBusiness
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.FaqInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqResponseType
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.enums.SeoShark
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections4.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

import java.math.BigDecimal

class SeoProvinceFaqBusinessTest extends Specification {

    def service = Mock(SeoService)
    def seoProvinceVehicleBusiness = Mock(SeoProvinceVehicleBusiness)
    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def testInstance = new SeoProvinceFaqBusiness(
            service: service,
            seoProvinceVehicleBusiness: seoProvinceVehicleBusiness,
            osdShoppingProxy: osdShoppingProxy
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic
    MockedStatic<LanguageUtils> languageUtilsMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        languageUtilsMockedStatic = Mockito.mockStatic(LanguageUtils.class)
        
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("Test FAQ Content")
        languageUtilsMockedStatic.when { LanguageUtils.sharkValFormat(_ as String, _ as Object[]) }.thenReturn("Formatted FAQ Content")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
        languageUtilsMockedStatic.close()
    }

    def "test constructor"() {
        when:
        def business = new SeoProvinceFaqBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.COUNTRY)
    }

    def "test doBusiness with null queryParameter"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US"),
                head: new MobileRequestHead()
        )
        service.buildQueryParameter(1, null, null) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US"),
                head: new MobileRequestHead()
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        service.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness for PROVINCE page with valid data"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def vehicleResponse = createVehicleResponse()
        
        service.buildQueryParameter(1, null, null) >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.faqList != null
        result.faqList.size() == 0
        result.cacheTime == "3600"
        result.carCard == true
    }

    def "test doBusiness for STATION page with valid data"() {
        given:
        def request = new QuerySeoFaqRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def vehicleResponse = createVehicleResponse()
        
        service.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.baseResponse.message == "success"
        result.faqList != null
        result.faqList.size() == 0
    }

    def "test doBusiness for SCENERY page with valid data"() {
        given:
        def request = new QuerySeoFaqRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def vehicleResponse = createVehicleResponse()
        
        service.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.baseResponse.message == "success"
        result.faqList != null
        result.faqList.size() == 0
    }

    def "test doBusiness with null vehicleResponse"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        
        service.buildQueryParameter(1, null, null) >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> null
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.faqList != null
    }

    def "test buildFaqContent with different parameter lengths"() {
        when:
        def result1 = testInstance.buildFaqContent("Test content", 0)
        def result2 = testInstance.buildFaqContent("Test content {0}", 1, "param1")
        def result3 = testInstance.buildFaqContent("", 1, "param1")
        def result4 = testInstance.buildFaqContent("Test content {0}", 1, null)
        def result5 = testInstance.buildFaqContent("Test content {0} {1}", 1, "param1", "param2")

        then:
        result1 == "Test content"
        result2 == null
        result3 == null
        result4 == null
        result5 == null
    }

    def "test buildFaq with valid and invalid inputs"() {
        when:
        def result1 = testInstance.buildFaq("Question?", "Answer.")
        def result2 = testInstance.buildFaq("", "Answer.")
        def result3 = testInstance.buildFaq("Question?", "")
        def result4 = testInstance.buildFaq(null, "Answer.")

        then:
        result1 != null
        result1.question == "Question?"
        result1.answer == "Answer."
        result2 == null
        result3 == null
        result4 == null
    }

    private SeoProvinceQueryParameter createValidQueryParameter(SeoPage seoPage) {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(seoPage)
        
        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(123)
        
        def poiA = new SeoPoiA()
        poiA.setPoiId(1L)
        configItem.setPoiA(poiA)
        
        def poiB = new SeoPoiB()
        poiB.setPoiCode("LAX")
        poiB.setPoiType(1)
        configItem.setPoiB(poiB)
        
        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private QueryRecomdProductsResponseType createVehicleResponse() {
        def vehicle = new RecomdVehicleDTO()
        vehicle.setGroupCode("ECAR")
        vehicle.setVehicleName("Toyota Camry")
        
        def price = new RecomdPriceDTO()
        price.setCurrentDailyPrice(new BigDecimal("50.00"))
        
        def product = new RecomdProduct()
        product.setVehicle(vehicle)
        product.setPrice(price)
        
        def productRes = new RecomdProductRes()
        productRes.setProducts([product])
        
        def response = new QueryRecomdProductsResponseType()
        response.setRecomdProductResList([productRes])
        return response
    }

    def "test provinceFaq method generates correct FAQ list"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def vehicleResponse = createVehicleResponse()

        service.buildQueryParameter(1, null, null) >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.faqList != null
        // Province FAQ should generate 10 FAQ items (some may be filtered out if null)
        result.faqList.size() >= 0
        result.faqList.every { it instanceof FaqInfo }
        result.faqList.every { it.question != null && it.answer != null }
    }

    def "test stationFaq method generates correct FAQ list"() {
        given:
        def request = new QuerySeoFaqRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def vehicleResponse = createVehicleResponse()

        service.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.faqList != null
        // Station FAQ should generate 10 FAQ items (some may be filtered out if null)
        result.faqList.size() >= 0
        result.faqList.every { it instanceof FaqInfo }
        result.faqList.every { it.question != null && it.answer != null }
    }

    def "test sceneryFaq method generates correct FAQ list"() {
        given:
        def request = new QuerySeoFaqRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def vehicleResponse = createVehicleResponse()

        service.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, "en-US", "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.faqList != null
        // Scenery FAQ should generate 10 FAQ items (some may be filtered out if null)
        result.faqList.size() >= 0
        result.faqList.every { it instanceof FaqInfo }
        result.faqList.every { it.question != null && it.answer != null }
    }

    def "test vehicle group FAQ methods with empty vehicle data"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)

        service.buildQueryParameter(1, null, null) >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> null
        service.getPoiAName(queryParameter, "en-US") >> "Test Province"
        service.getPoiBName(queryParameter, "en-US") >> "Test Airport"
        service.getCityName(123, "en-US") >> "Test City"
        service.getLowPriceVehicle(_ as List, _ as Set) >> ""  // Empty vehicle names
        service.getLowPriceVehicle(_ as List) >> ""
        service.getLowPrice(_ as List, "en-US", "USD") >> "\$0.00"
        service.queryCityTop1PoiName(123, "en-US") >> "Test Top Airport"
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.faqList != null
        // Should still generate FAQ list even with empty vehicle data
        result.baseResponse.message == "success"
    }

    def "test with different locales"() {
        given:
        def request = new QuerySeoFaqRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: locale, currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def vehicleResponse = createVehicleResponse()

        service.buildQueryParameter(1, null, null) >> queryParameter
        seoProvinceVehicleBusiness.queryVehicle(request.baseRequest, request.head, queryParameter, false) >> vehicleResponse
        setupServiceMocks(queryParameter, locale, "USD")
        osdShoppingProxy.getCacheTime(request.baseRequest, "LAX", null) >> 3600
        osdShoppingProxy.getCarCard(request.baseRequest, "LAX", null) >> "Test Car Card"

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.faqList != null

        where:
        locale << ["en-US", "zh-CN", "ja-JP", "ko-KR", ""]
    }

    private void setupServiceMocks(SeoProvinceQueryParameter queryParameter, String locale, String currencyCode) {
        service.getPoiAName(queryParameter, locale) >> "Test Province"
        service.getPoiBName(queryParameter, locale) >> "Test Airport"
        service.getCityName(123, locale) >> "Test City"
        service.getLowPriceVehicle(_ as List, _ as Set) >> "Test Vehicle"
        service.getLowPriceVehicle(_ as List) >> "Test Low Price Vehicle"
        service.getLowPrice(_ as List, locale, currencyCode) >> "\$50.00"
        service.queryCityTop1PoiName(123, locale) >> "Test Top Airport"
    }
}
