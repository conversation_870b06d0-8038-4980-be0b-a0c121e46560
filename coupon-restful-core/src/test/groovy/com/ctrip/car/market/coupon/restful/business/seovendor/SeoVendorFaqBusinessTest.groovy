package com.ctrip.car.market.coupon.restful.business.seovendor

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.business.seoservice.faq.SeoVendorFaqBusiness
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoVendorVehicleBusiness
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType
import com.ctrip.car.market.coupon.restful.dto.KeyValueDto
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoVendorFaqBusinessTest extends Specification {

    def service = Mock(SeoService)

    def seoVendorCache = Mock(SeoVendorCache)

    def vendorVehicleBusiness = Mock(SeoVendorVehicleBusiness)

    def tripConfig = Mock(TripConfig)

    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def testInstance = new SeoVendorFaqBusiness(
            service: service,
            seoVendorCache: seoVendorCache,
            vendorVehicleBusiness: vendorVehicleBusiness,
            tripConfig: tripConfig,
            osdShoppingProxy: osdShoppingProxy
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def test_vendorPage() {
        given:
        service.queryVendorHotCity(_ as String) >> new SeoHotVendorInformationDO(poiType: 1, poiCode: "SHA", vehicleId: 1L, vehicleGroupId: 2L, cityId: 1)
        vendorVehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as List<SeoHotVendorInformationDO>, true) >> getVehicleResponse()
        tripConfig.getVehicleGroupMappingList() >> [new KeyValueDto(key: "2", value: "small"), new KeyValueDto(key: "2", value: "suv"), new KeyValueDto(key: "2", value: "premium")]
        service.queryVehicleName(_ as Long, _ as String) >> "test"
        service.getCityName(_ as Integer, _ as String) >> "test"
        service.getAirportName(_ as String, _ as String) >> "test"
        osdShoppingProxy.getCarCard(_ as BaseRequest, _ as String, _ as String) >> true
        seoVendorCache.queryVendor(_ as String) >> vendorInfo
        service.currencyString(_ as BigDecimal, _ as String, _ as String) >> "test"

        service.queryPoiName(_ as Integer, _ as String, _ as String) >> "test"
        seoVendorCache.queryVendorCity(_ as String, _ as Integer) >> new SeoHotVendorCityDO(cityId: 1)
        service.queryVendorCity(_ as String, _ as Integer) >> new SeoHotVendorInformationDO(poiType: 1, poiCode: "SHA", vehicleId: 1L, vehicleGroupId: 2L, cityId: 1)


        expect:
        CollectionUtils.isNotEmpty(testInstance.queryFaq(request as QuerySeoFaqRequestType).getFaqList()) == result

        where:
        request                                                                                                                                                        | vendorInfo                                                 || result
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001")                                                                | null                                                       || false
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD"), head: new MobileRequestHead(), vendorCode: "SD0001")            | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") || true
        new QuerySeoFaqRequestType(baseRequest: new BaseRequest(locale: "zh-HK", currencyCode: "HKD"), head: new MobileRequestHead(), vendorCode: "SD0001", cityId: 1) | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") || true

    }

    def getVehicleResponse() {
        return [new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "2", vehicleName: "test"), price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.ONE, currentOriginalDailyPrice: BigDecimal.TWO))])])]
    }
}
