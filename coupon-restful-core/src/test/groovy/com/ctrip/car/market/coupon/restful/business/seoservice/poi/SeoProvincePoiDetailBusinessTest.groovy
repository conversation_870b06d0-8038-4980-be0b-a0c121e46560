package com.ctrip.car.market.coupon.restful.business.seoservice.poi

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.PoiDetail
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailResponseType
import com.ctrip.car.market.coupon.restful.dto.SeoPoi
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.dcs.geo.domain.value.City
import spock.lang.Specification

class SeoProvincePoiDetailBusinessTest extends Specification {

    def seoService = Mock(SeoService)

    def testInstance = new SeoProvincePoiDetailBusiness(
            seoService: seoService
    )

    def "test constructor"() {
        when:
        def business = new SeoProvincePoiDetailBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.COUNTRY)
        !business.isSupport(SeoPage.VENDOR)
    }

    def "test doBusiness with null queryParameter"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        seoService.buildQueryParameter(1, null, null) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        seoService.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness for PROVINCE page with valid data"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def seoPoi = createSeoPoi()
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        setupServiceMocksForProvince(queryParameter, "en-US")
        seoService.getSeoPoi(1, "LAX", "en-US") >> seoPoi

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.poiDetail != null
        
        def poiDetail = result.poiDetail
        poiDetail.countryId == 1
        poiDetail.provinceId == 2
        poiDetail.cityId == 123
        poiDetail.countryName == "Test Country"
        poiDetail.provinceName == "Test Province"
        poiDetail.cityName == "Test City"
        poiDetail.isHMT == false
        poiDetail.countryUrlName == "test-country"
        poiDetail.cityUrlName == "test-city"
        poiDetail.poiType == 1
        poiDetail.poiCode == "LAX"
        poiDetail.poiName == "Test Airport"
        poiDetail.longitude == 118.25
        poiDetail.latitude == 34.05
        poiDetail.poiTitle == "Test Province"
    }

    def "test doBusiness for STATION page with valid data"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def seoPoi = createSeoPoi()
        def city = createCity()
        
        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        setupServiceMocksForStation(queryParameter, "en-US")
        seoService.getCity(123) >> city
        seoService.getSeoPoi(1, "LAX", "en-US") >> seoPoi
        seoService.getPoiAName(queryParameter, "en-US") >> "Test Station"

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.baseResponse.message == "success"
        result.poiDetail != null
        
        def poiDetail = result.poiDetail
        poiDetail.countryId == 1
        poiDetail.provinceId == 5
        poiDetail.cityId == 123
        poiDetail.poiTitle == "Test Station"
    }

    def "test doBusiness for SCENERY page with valid data"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def seoPoi = createSeoPoi()
        def city = createCity()
        
        seoService.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        setupServiceMocksForScenery(queryParameter, "en-US")
        seoService.getCity(123) >> city
        seoService.getSeoPoi(1, "LAX", "en-US") >> seoPoi
        seoService.getPoiAName(queryParameter, "en-US") >> "Test Scenery"

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.baseResponse.message == "success"
        result.poiDetail != null
        
        def poiDetail = result.poiDetail
        poiDetail.poiTitle == "Test Scenery"
    }

    def "test doBusiness with null seoPoi"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        setupServiceMocksForProvince(queryParameter, "en-US")
        seoService.getSeoPoi(1, "LAX", "en-US") >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.poiDetail != null
        result.poiDetail.longitude == null
        result.poiDetail.latitude == null
    }

    def "test doBusiness with null city for STATION page"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        
        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        setupServiceMocksForStation(queryParameter, "en-US")
        seoService.getCity(123) >> null
        seoService.getSeoPoi(1, "LAX", "en-US") >> null
        seoService.getPoiAName(queryParameter, "en-US") >> "Test Station"

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.baseResponse.message == "success"
        result.poiDetail != null
        result.poiDetail.provinceId == 0
    }

    def "test doBusiness with different locales"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: locale)
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        
        seoService.buildQueryParameter(1, null, null) >> queryParameter
        setupServiceMocksForProvince(queryParameter, locale)
        seoService.getSeoPoi(1, "LAX", locale) >> createSeoPoi()

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse.message == "success"
        result.poiDetail != null

        where:
        locale << ["en-US", "zh-CN", "ja-JP", "ko-KR", ""]
    }

    def "test doBusiness with HMT provinces"() {
        given:
        def request = new QuerySeoPoiDetailRequestType(
                provinceId: provinceId,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        queryParameter.queryParameter.poiA.poiId = provinceId.longValue()
        
        seoService.buildQueryParameter(provinceId, null, null) >> queryParameter
        setupServiceMocksForProvince(queryParameter, "en-US")
        seoService.isHmt(provinceId) >> isHMT
        seoService.getSeoPoi(1, "LAX", "en-US") >> createSeoPoi()

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.poiDetail != null
        result.poiDetail.isHMT == isHMT

        where:
        provinceId | isHMT
        53         | true   // Hong Kong
        32         | true   // Macau
        33         | true   // Taiwan
        1          | false  // Other province
        100        | false  // Other province
    }

    private SeoProvinceQueryParameter createValidQueryParameter(SeoPage seoPage) {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(seoPage)
        
        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(123)
        configItem.setCountryId(1)
        
        def poiA = new SeoPoiA()
        poiA.setPoiId(2L)
        configItem.setPoiA(poiA)
        
        def poiB = new SeoPoiB()
        poiB.setPoiCode("LAX")
        poiB.setPoiType(1)
        configItem.setPoiB(poiB)
        
        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private SeoPoi createSeoPoi() {
        def seoPoi = new SeoPoi()
        seoPoi.setPoiType(1)
        seoPoi.setPoiCode("LAX")
        seoPoi.setPoiName("Los Angeles International Airport")
        seoPoi.setLat(34.05)
        seoPoi.setLon(118.25)
        return seoPoi
    }

    private City createCity() {
        def city = Mock(City)
        city.getProvinceId() >> 5L
        return city
    }

    private void setupServiceMocksForProvince(SeoProvinceQueryParameter queryParameter, String locale) {
        seoService.getCountryName(1, locale) >> "Test Country"
        seoService.getProvinceName(2, locale) >> "Test Province"
        seoService.getCityName(123, locale) >> "Test City"
        seoService.isHmt(2) >> false
        seoService.queryUrlName(1L, 1) >> "test-country"
        seoService.queryUrlName(123L, 2) >> "test-city"
        seoService.queryPoiName(1, "LAX", locale) >> "Test Airport"
    }

    private void setupServiceMocksForStation(SeoProvinceQueryParameter queryParameter, String locale) {
        seoService.getCountryName(1, locale) >> "Test Country"
        seoService.getCityName(123, locale) >> "Test City"
        seoService.getProvinceName(5, locale) >> "Test Province"
        seoService.isHmt(5) >> false
        seoService.queryUrlName(1L, 1) >> "test-country"
        seoService.queryUrlName(123L, 2) >> "test-city"
        seoService.queryPoiName(1, "LAX", locale) >> "Test Airport"
    }

    private void setupServiceMocksForScenery(SeoProvinceQueryParameter queryParameter, String locale) {
        seoService.getCountryName(1, locale) >> "Test Country"
        seoService.getCityName(123, locale) >> "Test City"
        seoService.isHmt(5) >> false
        seoService.queryUrlName(1L, 1) >> "test-country"
        seoService.queryUrlName(123L, 2) >> "test-city"
        seoService.queryPoiName(1, "LAX", locale) >> "Test Airport"
    }
}
