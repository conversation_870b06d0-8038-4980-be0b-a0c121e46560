package com.ctrip.car.market.coupon.restful.business.seoservice.vehicle

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.CityVehicleInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListResponseType
import com.ctrip.car.market.coupon.restful.contract.seo.VehicleInfo
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.utils.BaseUtils
import com.ctrip.car.osd.shopping.api.entity.*
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import com.google.common.collect.Lists
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

import java.util.Calendar

class SeoProvinceVehicleBusinessTest extends Specification {

    def seoService = Mock(SeoService)
    def osdShoppingProxy = Mock(OsdShoppingProxy)
    def seoCityVehicleBusiness = Mock(SeoCityVehicleBusiness)

    def testInstance = new SeoProvinceVehicleBusiness(
            seoService: seoService,
            osdShoppingProxy: osdShoppingProxy,
            seoCityVehicleBusiness: seoCityVehicleBusiness
    )

    MockedStatic<BaseUtils> baseUtilsMockedStatic

    def setup() {
        baseUtilsMockedStatic = Mockito.mockStatic(BaseUtils.class)
        baseUtilsMockedStatic.when { BaseUtils.getUidByCommon(_ as MobileRequestHead) }.thenReturn("test-uid")
    }

    def cleanup() {
        baseUtilsMockedStatic.close()
    }

    def "test constructor"() {
        when:
        def business = new SeoProvinceVehicleBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.COUNTRY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.VENDOR)
    }

    def "test doBusiness with null queryParameter"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        seoService.buildQueryParameter(1, null, null) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.responseStatus.value == "FAIL"
        result.baseResponse.responseStatus.message == "para error"
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        seoService.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.responseStatus.value == "FAIL"
        result.baseResponse.responseStatus.message == "para error"
    }

    def "test doBusiness with successful response for PROVINCE"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()
        def vendorList = createVendorList()

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> vendorList

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == vehicleList
        result.url == "https://example.com/search"
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.cityVehicleList[0].cityId == 123
        result.cityVehicleList[0].url == "https://example.com/search"
        result.cityVehicleList[0].vehicleList == vehicleList
        result.vendorList == vendorList
    }

    def "test doBusiness with successful response for STATION"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.vehicleList == vehicleList
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.vendorList != null
    }

    def "test doBusiness with successful response for SCENERY"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.vehicleList == vehicleList
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.vendorList != null
    }

    def "test doBusiness with null soaResponse"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> null
        seoCityVehicleBusiness.resConvert(null, request) >> []
        seoService.getTopVendor(null) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == []
        result.url == null
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.cityVehicleList[0].cityId == 123
        result.cityVehicleList[0].url == null
        result.cityVehicleList[0].vehicleList == []
        result.vendorList == []
    }

    def "test doBusiness with empty soaResponse"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def emptySoaResponse = new QueryRecomdProductsResponseType()
        emptySoaResponse.setRecomdProductResList([])

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> emptySoaResponse
        seoCityVehicleBusiness.resConvert(emptySoaResponse, request) >> []
        seoService.getTopVendor(emptySoaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == []
        result.url == null
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.vendorList == []
    }

    def "test queryVehicle method"() {
        given:
        def baseRequest = new BaseRequest(
                requestId: "test-request-id",
                channelId: 12345,
                locale: "en-US",
                currencyCode: "USD"
        )
        def head = new MobileRequestHead()
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def expectedSoaResponse = createSuccessfulSoaResponse()

        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, true) >> expectedSoaResponse

        when:
        def result = testInstance.queryVehicle(baseRequest, head, queryParameter, true)

        then:
        result == expectedSoaResponse
        1 * osdShoppingProxy.queryRecomdProducts({ QueryRecomdProductsRequestType req ->
            req.baseRequest.requestId == "test-request-id" &&
            req.baseRequest.channelId == 12345 &&
            req.baseRequest.locale == "en-US" &&
            req.baseRequest.currencyCode == "USD" &&
            req.baseRequest.uid == "test-uid" &&
            req.sence == 2 &&
            req.queryParams != null &&
            req.queryParams.size() == 1 &&
            req.queryParams[0].pickupLocation.cityId == 123 &&
            req.queryParams[0].pickupLocation.locationCode == "LAX" &&
            req.queryParams[0].pickupLocation.locationType == 1
        }, true)
    }