package com.ctrip.car.market.coupon.restful.business.seoservice.vehicle

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.CityVehicleInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListResponseType
import com.ctrip.car.market.coupon.restful.contract.seo.VehicleInfo
import com.ctrip.car.market.coupon.restful.contract.seo.VendorVehicleDto
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter
import com.ctrip.car.market.coupon.restful.enums.SeoPage
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.utils.BaseUtils
import com.ctrip.car.osd.shopping.api.entity.*
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import com.google.common.collect.Lists
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

import java.util.Calendar

class SeoProvinceVehicleBusinessTest extends Specification {

    def seoService = Mock(SeoService)
    def osdShoppingProxy = Mock(OsdShoppingProxy)
    def seoCityVehicleBusiness = Mock(SeoCityVehicleBusiness)

    def testInstance = new SeoProvinceVehicleBusiness(
            seoService: seoService,
            osdShoppingProxy: osdShoppingProxy,
            seoCityVehicleBusiness: seoCityVehicleBusiness
    )

    MockedStatic<BaseUtils> baseUtilsMockedStatic

    def setup() {
        baseUtilsMockedStatic = Mockito.mockStatic(BaseUtils.class)
    }

    def cleanup() {
        baseUtilsMockedStatic.close()
    }

    def "test constructor"() {
        when:
        def business = new SeoProvinceVehicleBusiness()

        then:
        business.isSupport(SeoPage.PROVINCE)
        business.isSupport(SeoPage.STATION)
        business.isSupport(SeoPage.SCENERY)
        !business.isSupport(SeoPage.COUNTRY)
        !business.isSupport(SeoPage.CITY)
        !business.isSupport(SeoPage.VENDOR)
    }

    def "test doBusiness with null queryParameter"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        seoService.buildQueryParameter(1, null, null) >> null

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness with null queryParameter.queryParameter"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US")
        )
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(SeoPage.PROVINCE)
        queryParameter.setQueryParameter(null)
        seoService.buildQueryParameter(1, null, null) >> queryParameter

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.baseResponse != null
        result.baseResponse.message == "para error"
    }

    def "test doBusiness with successful response for PROVINCE"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()
        def vendorList = createVendorList()

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> vendorList

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == vehicleList
        result.url == "https://example.com/search"
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.cityVehicleList[0].cityId == 123
        result.cityVehicleList[0].url == "https://example.com/search"
        result.cityVehicleList[0].vehicleList == vehicleList
        result.vendorList == vendorList
    }

    def "test doBusiness with successful response for STATION"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                poiType: 2,
                poiCode: "STATION123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.STATION)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(null, 2, "STATION123") >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.STATION)

        then:
        result != null
        result.vehicleList == vehicleList
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.vendorList != null
    }

    def "test doBusiness with successful response for SCENERY"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                poiType: 20,
                poiCode: "SCENERY123",
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.SCENERY)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(null, 20, "SCENERY123") >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.SCENERY)

        then:
        result != null
        result.vehicleList == vehicleList
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.vendorList != null
    }

    def "test doBusiness with null soaResponse"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> null
        seoCityVehicleBusiness.resConvert(null, request) >> []
        seoService.getTopVendor(null) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == []
        result.url == null
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.cityVehicleList[0].cityId == 123
        result.cityVehicleList[0].url == null
        result.cityVehicleList[0].vehicleList == []
        result.vendorList == []
    }

    def "test doBusiness with empty soaResponse"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def emptySoaResponse = new QueryRecomdProductsResponseType()
        emptySoaResponse.setRecomdProductResList([])

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> emptySoaResponse
        seoCityVehicleBusiness.resConvert(emptySoaResponse, request) >> []
        seoService.getTopVendor(emptySoaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == []
        result.url == null
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1
        result.vendorList == []
    }

    def "test reqConvert method"() {
        given:
        def testMethod = testInstance.getClass().getDeclaredMethod("reqConvert",
                com.ctrip.car.market.coupon.restful.contract.BaseRequest.class,
                MobileRequestHead.class,
                SeoProvinceQueryParameter.class)
        testMethod.setAccessible(true)

        def baseRequest = new BaseRequest(
                requestId: "test-request-id",
                channelId: 12345,
                locale: "en-US",
                currencyCode: "USD"
        )
        def head = new MobileRequestHead()
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def pickupDate = Calendar.getInstance()

        seoService.getPickupDate(7) >> pickupDate

        when:
        def result = testMethod.invoke(testInstance, baseRequest, head, queryParameter) as QueryRecomdProductsRequestType

        then:
        result != null
        result.baseRequest != null
        result.baseRequest.requestId == "test-request-id"
        result.baseRequest.channelId == 12345
        result.baseRequest.locale == "en-US"
        result.baseRequest.currencyCode == "USD"
        result.sence == 2
        result.queryParams != null
        result.queryParams.size() == 1

        def queryParam = result.queryParams[0]
        queryParam.pickupLocation != null
        queryParam.pickupLocation.cityId == 123
        queryParam.pickupLocation.locationCode == "LAX"
        queryParam.pickupLocation.locationType == 1
        queryParam.pickupLocation.date == pickupDate
    }

    def "test doBusiness with different request parameters"() {
        given:
        def queryParameter = createValidQueryParameter(seoPage)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(provinceId, poiType, poiCode) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, seoPage)

        then:
        result != null
        result.vehicleList == vehicleList
        result.cityVehicleList != null
        result.cityVehicleList.size() == 1

        where:
        request                                                                                                                                                         | seoPage          | provinceId | poiType | poiCode
        new QuerySeoVehicleListRequestType(provinceId: 1, baseRequest: new BaseRequest(locale: "en-US"), head: new MobileRequestHead())                                 | SeoPage.PROVINCE | 1          | null    | null
        new QuerySeoVehicleListRequestType(poiType: 2, poiCode: "STATION", baseRequest: new BaseRequest(locale: "en-US"), head: new MobileRequestHead())                | SeoPage.STATION  | null       | 2       | "STATION"
        new QuerySeoVehicleListRequestType(poiType: 20, poiCode: "SCENERY", baseRequest: new BaseRequest(locale: "en-US"), head: new MobileRequestHead())               | SeoPage.SCENERY  | null       | 20      | "SCENERY"
        new QuerySeoVehicleListRequestType(provinceId: 5, poiType: 1, poiCode: "AIRPORT", baseRequest: new BaseRequest(locale: "en-US"), head: new MobileRequestHead()) | SeoPage.PROVINCE | 5          | 1       | "AIRPORT"
    }

    def "test doBusiness with different locales and currencies"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: locale, currencyCode: currency),
                head: new MobileRequestHead()
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == vehicleList

        where:
        locale  | currency
        "en-US" | "USD"
        "zh-CN" | "CNY"
        "ja-JP" | "JPY"
        "ko-KR" | "KRW"
        "fr-FR" | "EUR"
    }

    def "test doBusiness with null head parameter"() {
        given:
        def request = new QuerySeoVehicleListRequestType(
                provinceId: 1,
                baseRequest: new BaseRequest(locale: "en-US", currencyCode: "USD"),
                head: null
        )
        def queryParameter = createValidQueryParameter(SeoPage.PROVINCE)
        def soaResponse = createSuccessfulSoaResponse()
        def vehicleList = createVehicleList()

        seoService.buildQueryParameter(1, null, null) >> queryParameter
        seoService.getPickupDate(7) >> Calendar.getInstance()
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, false) >> soaResponse
        seoCityVehicleBusiness.resConvert(soaResponse, request) >> vehicleList
        seoCityVehicleBusiness.buildUrl(1, soaResponse.recomdProductResList[0], null, request.baseRequest) >> "https://example.com/search"
        seoService.getTopVendor(soaResponse) >> []

        when:
        def result = testInstance.doBusiness(request, SeoPage.PROVINCE)

        then:
        result != null
        result.vehicleList == vehicleList
        result.cityVehicleList != null
    }

    private SeoProvinceQueryParameter createValidQueryParameter(SeoPage seoPage) {
        def queryParameter = new SeoProvinceQueryParameter()
        queryParameter.setSeoPage(seoPage)

        def configItem = new SeoPoiConfigItem()
        configItem.setCityId(123)
        configItem.setCountryId(1)

        def poiA = new SeoPoiA()
        poiA.setPoiId(2L)
        configItem.setPoiA(poiA)

        def poiB = new SeoPoiB()
        poiB.setPoiCode("LAX")
        poiB.setPoiType(1)
        configItem.setPoiB(poiB)

        queryParameter.setQueryParameter(configItem)
        return queryParameter
    }

    private QueryRecomdProductsResponseType createSuccessfulSoaResponse() {
        def response = new QueryRecomdProductsResponseType()
        response.setSourceCountryId(1)

        def productRes = new RecomdProductRes()
        productRes.setPickupLocation(createLocationInfo())
        productRes.setReturnLocation(createLocationInfo())
        productRes.setProducts(createRecomdProducts())

        response.setRecomdProductResList([productRes])
        return response
    }

    private LocationRequestInfo createLocationInfo() {
        def location = new LocationRequestInfo()
        location.setCityId(123)
        location.setCityName("Los Angeles")
        location.setLocationCode("LAX")
        location.setLocationType(1)
        return location
    }

    private List<RecomdProduct> createRecomdProducts() {
        def product1 = new RecomdProduct()
        product1.setVehicle(createVehicleDTO("ECAR", "Economy"))
        product1.setPrice(createPriceDTO(BigDecimal.valueOf(50.0)))
        product1.setVendorInfo(createVendorInfo("HERTZ", "Hertz"))

        def product2 = new RecomdProduct()
        product2.setVehicle(createVehicleDTO("CCAR", "Compact"))
        product2.setPrice(createPriceDTO(BigDecimal.valueOf(60.0)))
        product2.setVendorInfo(createVendorInfo("AVIS", "Avis"))

        return [product1, product2]
    }

    private RecomdVehicleDTO createVehicleDTO(String vehicleCode, String vehicleName) {
        def vehicle = new RecomdVehicleDTO()
        vehicle.setVehicleCode(vehicleCode)
        vehicle.setVehicleName(vehicleName)
        vehicle.setGroupCode("ECAR")
        vehicle.setGroupName("Economy")
        vehicle.setVendorCode("HERTZ")
        return vehicle
    }

    private RecomdPriceDTO createPriceDTO(BigDecimal price) {
        def priceDTO = new RecomdPriceDTO()
        priceDTO.setCurrentDailyPrice(price)
        priceDTO.setCurrentOriginalDailyPrice(price.multiply(BigDecimal.valueOf(1.1)))
        priceDTO.setCurrency("USD")
        priceDTO.setPrefix("from ")
        priceDTO.setSuffix("/day")
        return priceDTO
    }

    private SimpleVendor createVendorInfo(String vendorCode, String vendorName) {
        def vendorInfo = new SimpleVendor()
        vendorInfo.setBizVendorCode(vendorCode)
        vendorInfo.setVendorName(vendorName)
        return vendorInfo
    }

    private List<VehicleInfo> createVehicleList() {
        def vehicle1 = new VehicleInfo()
        vehicle1.setVehicleCode("ECAR")
        vehicle1.setVehicleName("Economy Car")
        vehicle1.setGroupCode("ECAR")
        vehicle1.setGroupName("Economy")

        def vehicle2 = new VehicleInfo()
        vehicle2.setVehicleCode("CCAR")
        vehicle2.setVehicleName("Compact Car")
        vehicle2.setGroupCode("CCAR")
        vehicle2.setGroupName("Compact")

        return [vehicle1, vehicle2]
    }

    private List<VendorVehicleDto> createVendorList() {
        def vendor1 = new VendorVehicleDto()
        vendor1.setVendorCode("HERTZ")
        vendor1.setVendorName("Hertz")
        vendor1.setCarType("Economy Car")
        vendor1.setPrice(BigDecimal.valueOf(50.0))

        def vendor2 = new VendorVehicleDto()
        vendor2.setVendorCode("AVIS")
        vendor2.setVendorName("Avis")
        vendor2.setCarType("Compact Car")
        vendor2.setPrice(BigDecimal.valueOf(60.0))

        return [vendor1, vendor2]
    }
}