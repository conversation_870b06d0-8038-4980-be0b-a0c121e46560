package com.ctrip.car.market.coupon.restful.business.seoservice.recommendairport;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoCityVehicleBusiness;
import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendAirportRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendAirportResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.RecommendAirportInfo;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotProvinceinfoDO;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SeoRecommendAirportBusiness extends SeoBaseService<QuerySeoRecommendAirportRequestType, QuerySeoRecommendAirportResponseType> {

    public SeoRecommendAirportBusiness() {
        super(Lists.newArrayList(SeoPage.COUNTRY, SeoPage.PROVINCE));
    }

    @Resource
    private SeoService service;

    @Resource
    private TripConfig tripConfig;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    @Resource
    private SeoCityVehicleBusiness seoVehicleBusiness;

    @Override
    public QuerySeoRecommendAirportResponseType doBusiness(QuerySeoRecommendAirportRequestType request, SeoPage seoPage) {
        return queryAirport(request);
    }

    public QuerySeoRecommendAirportResponseType queryAirport(QuerySeoRecommendAirportRequestType request) {
        QuerySeoRecommendAirportResponseType response = new QuerySeoRecommendAirportResponseType();
        if (request.getBaseRequest() == null) {
            response.setBaseResponse(ResponseUtil.fail("baseRequest is null"));
            return response;
        }
        if (Optional.ofNullable(request.getCountryId()).orElse(0) <= 0 && Optional.ofNullable(request.getProvinceId()).orElse(0) <= 0) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        Integer countryId = request.getCountryId();
        if (Optional.ofNullable(request.getProvinceId()).orElse(0) > 0) {
            SeoHotProvinceinfoDO provinceDO = service.queryProvince(request.getProvinceId());
            if (provinceDO != null) {
                countryId = provinceDO.getCountryId();
            }
        }
        if (countryId == null) {
            return response;
        }
        List<SeoHotDestinatioinfoDO> destinationList = service.queryHotDestination(countryId, null, null, null);
        if (CollectionUtils.isEmpty(destinationList)) {
            return response;
        }
        //台湾站特殊处理
        if ("zh-tw".equalsIgnoreCase(request.getBaseRequest().getLocale()) && Objects.equals(countryId, 1)) {
            destinationList = destinationList.stream().filter(l -> !tripConfig.getTwCityList().contains(l.getCityId())).collect(Collectors.toList());
        }
        String locale = request.getBaseRequest().getLocale();
        String countryName = service.getCountryName(countryId, locale);
        response.setAirportList(convert(destinationList, request.getBaseRequest()));
        response.setTitle(LanguageUtils.sharkValFormat(SeoShark.RecommendAirportTitle.getValue(locale), countryName));
        response.setSubTitle(SeoShark.RecommendAirportSubTitle.getValue(locale));
        response.setBaseResponse(ResponseUtil.success());
        Metrics.build().withTag("country", Optional.ofNullable(request.getCountryId()).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getAirportList()) ? "1" : "0")
                .recordOne(MetricsEnum.SEO_RECOMMEND_AIRPORT.getTitle());
        return response;
    }

    private List<RecommendAirportInfo> convert(List<SeoHotDestinatioinfoDO> destinationList, BaseRequest baseRequest) {
        List<SeoHotDestinatioinfoDO> list = Lists.newArrayList();
        Set<String> airportSet = Sets.newHashSet();
        for (SeoHotDestinatioinfoDO item : destinationList) {
            if (airportSet.contains(item.getPoiCode())) {
                continue;
            }
            list.add(item);
            airportSet.add(item.getPoiCode());
        }
        return list.stream().sorted(Comparator.comparing(SeoHotDestinatioinfoDO::getOrderNum).reversed()).limit(tripConfig.getMaxRecommendAirportCount()).map(l -> {
            RecommendAirportInfo item = new RecommendAirportInfo();
            item.setAirportName(getAirportName(l.getPoiCode(), baseRequest.getLocale()));
            item.setUrl(service.getSiteUrl(l.getUrl(), baseRequest.getLocale()));
            item.setImgUrl(null);
            RecomdPriceDTO priceDTO = getAirportPrice(l, baseRequest);
            if (priceDTO != null) {
                BigDecimal price = priceDTO.getCurrentOriginalDailyPrice() != null ? priceDTO.getCurrentOriginalDailyPrice() : priceDTO.getCurrentDailyPrice();
                item.setPriceStr(service.currencyString(price, baseRequest.getLocale(), baseRequest.getCurrencyCode()));
                item.setPrefix(priceDTO.getPrefix());
                item.setSuffix(priceDTO.getSuffix());
            }
            return item;
        }).collect(Collectors.toList());
    }

    private String getAirportName(String airportCode, String locale) {
        String airportName = service.getAirportName(airportCode, locale);
        return LanguageUtils.sharkValFormat(SeoShark.RecommendAirportItemTitle.getValue(locale), airportName);
    }

    private RecomdPriceDTO getAirportPrice(SeoHotDestinatioinfoDO destinationInfo, BaseRequest baseRequest) {
        RecomdPriceDTO priceDTO = osdShoppingProxy.getAirportPrice(baseRequest, destinationInfo.getPoiCode());
        if (priceDTO != null) {
            return priceDTO;
        }
        QueryRecomdProductsResponseType response = seoVehicleBusiness.queryVehicle(baseRequest, null, destinationInfo, true);
        if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList()) || CollectionUtils.isEmpty(response.getRecomdProductResList().get(0).getProducts())) {
            return null;
        }
        RecomdProduct recomdProduct = response.getRecomdProductResList().get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).min(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).orElse(null);
        return recomdProduct == null ? null : recomdProduct.getPrice();
    }
}
