package com.ctrip.car.market.coupon.restful.business.seoservice.poi;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.contract.seo.PoiDetail;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailResponseType;
import com.ctrip.car.market.coupon.restful.dto.SeoPoi;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.dcs.geo.domain.value.City;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

@Component
public class SeoProvincePoiDetailBusiness extends SeoBaseService<QuerySeoPoiDetailRequestType, QuerySeoPoiDetailResponseType> {

    public SeoProvincePoiDetailBusiness() {
        super(Lists.newArrayList(SeoPage.PROVINCE, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Resource
    private SeoService seoService;

    @Override
    public QuerySeoPoiDetailResponseType doBusiness(QuerySeoPoiDetailRequestType request, SeoPage seoPage) {
        QuerySeoPoiDetailResponseType response = new QuerySeoPoiDetailResponseType();
        SeoProvinceQueryParameter queryParameter = seoService.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
        if (queryParameter == null || queryParameter.getQueryParameter() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        String locale = request.getBaseRequest().getLocale();
        switch (queryParameter.getSeoPage()) {
            case PROVINCE:
                response.setPoiDetail(getProvincePoiDetail(queryParameter, locale));
                break;
            case STATION:
                response.setPoiDetail(getStationPoiDetail(queryParameter, locale));
                break;
            case SCENERY:
                response.setPoiDetail(getSceneryPoiDetail(queryParameter, locale));
                break;
        }
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private PoiDetail getProvincePoiDetail(SeoProvinceQueryParameter queryParameter, String locale) {
        PoiDetail poiDetail = new PoiDetail();
        poiDetail.setCountryId(Optional.ofNullable(queryParameter.getQueryParameter().getCountryId()).orElse(0));
        poiDetail.setProvinceId(Optional.ofNullable(queryParameter.getQueryParameter().getPoiA().getPoiId()).orElse(0L).intValue());
        poiDetail.setCityId(Optional.ofNullable(queryParameter.getQueryParameter().getCityId()).orElse(0));
        poiDetail.setCountryName(seoService.getCountryName(poiDetail.getCountryId(), locale));
        poiDetail.setProvinceName(seoService.getProvinceName(poiDetail.getProvinceId(), locale));
        poiDetail.setCityName(seoService.getCityName(poiDetail.getCityId(), locale));
        poiDetail.setIsHMT(seoService.isHmt(poiDetail.getProvinceId()));
        poiDetail.setCountryUrlName(seoService.queryUrlName(poiDetail.getCountryId().longValue(), 1));
        poiDetail.setCityUrlName(seoService.queryUrlName(poiDetail.getCityId().longValue(), 2));
        poiDetail.setPoiType(queryParameter.getQueryParameter().getPoiB().getPoiType());
        poiDetail.setPoiCode(queryParameter.getQueryParameter().getPoiB().getPoiCode());
        poiDetail.setPoiName(seoService.queryPoiName(poiDetail.getPoiType(), poiDetail.getPoiCode(), locale));
        SeoPoi seoPoi = seoService.getSeoPoi(poiDetail.getPoiType(), poiDetail.getPoiCode(), locale);
        if (seoPoi != null) {
            poiDetail.setLongitude(seoPoi.getLon());
            poiDetail.setLatitude(seoPoi.getLat());
        }
        poiDetail.setPoiTitle(poiDetail.getProvinceName());
        return poiDetail;
    }

    private PoiDetail getStationPoiDetail(SeoProvinceQueryParameter queryParameter, String locale) {
        PoiDetail poiDetail = new PoiDetail();
        poiDetail.setCountryId(Optional.ofNullable(queryParameter.getQueryParameter().getCountryId()).orElse(0));
        poiDetail.setCityId(Optional.ofNullable(queryParameter.getQueryParameter().getCityId()).orElse(0));
        poiDetail.setCountryName(seoService.getCountryName(poiDetail.getCountryId(), locale));
        poiDetail.setCityName(seoService.getCityName(poiDetail.getCityId(), locale));
        City city = seoService.getCity(poiDetail.getCityId());
        poiDetail.setProvinceId(city != null ? city.getProvinceId().intValue() : 0);
        poiDetail.setProvinceName(seoService.getProvinceName(poiDetail.getProvinceId(), locale));
        poiDetail.setIsHMT(seoService.isHmt(poiDetail.getProvinceId()));
        poiDetail.setCountryUrlName(seoService.queryUrlName(poiDetail.getCountryId().longValue(), 1));
        poiDetail.setCityUrlName(seoService.queryUrlName(poiDetail.getCityId().longValue(), 2));
        poiDetail.setPoiType(queryParameter.getQueryParameter().getPoiB().getPoiType());
        poiDetail.setPoiCode(queryParameter.getQueryParameter().getPoiB().getPoiCode());
        poiDetail.setPoiName(seoService.queryPoiName(poiDetail.getPoiType(), poiDetail.getPoiCode(), locale));
        SeoPoi seoPoi = seoService.getSeoPoi(poiDetail.getPoiType(), poiDetail.getPoiCode(), locale);
        if (seoPoi != null) {
            poiDetail.setLongitude(seoPoi.getLon());
            poiDetail.setLatitude(seoPoi.getLat());
        }
        poiDetail.setPoiTitle(seoService.getPoiAName(queryParameter, locale));
        return poiDetail;
    }

    private PoiDetail getSceneryPoiDetail(SeoProvinceQueryParameter queryParameter, String locale) {
        PoiDetail poiDetail = new PoiDetail();
        poiDetail.setCountryId(Optional.ofNullable(queryParameter.getQueryParameter().getCountryId()).orElse(0));
        poiDetail.setCityId(Optional.ofNullable(queryParameter.getQueryParameter().getCityId()).orElse(0));
        poiDetail.setCountryName(seoService.getCountryName(poiDetail.getCountryId(), locale));
        poiDetail.setCityName(seoService.getCityName(poiDetail.getCityId(), locale));
        City city = seoService.getCity(poiDetail.getCityId());
        poiDetail.setProvinceId(city != null ? city.getProvinceId().intValue() : 0);
        poiDetail.setIsHMT(seoService.isHmt(poiDetail.getProvinceId()));
        poiDetail.setCountryUrlName(seoService.queryUrlName(poiDetail.getCountryId().longValue(), 1));
        poiDetail.setCityUrlName(seoService.queryUrlName(poiDetail.getCityId().longValue(), 2));
        poiDetail.setPoiType(queryParameter.getQueryParameter().getPoiB().getPoiType());
        poiDetail.setPoiCode(queryParameter.getQueryParameter().getPoiB().getPoiCode());
        poiDetail.setPoiName(seoService.queryPoiName(poiDetail.getPoiType(), poiDetail.getPoiCode(), locale));
        SeoPoi seoPoi = seoService.getSeoPoi(poiDetail.getPoiType(), poiDetail.getPoiCode(), locale);
        if (seoPoi != null) {
            poiDetail.setLongitude(seoPoi.getLon());
            poiDetail.setLatitude(seoPoi.getLat());
        }
        poiDetail.setPoiTitle(seoService.getPoiAName(queryParameter, locale));
        return poiDetail;
    }


}
