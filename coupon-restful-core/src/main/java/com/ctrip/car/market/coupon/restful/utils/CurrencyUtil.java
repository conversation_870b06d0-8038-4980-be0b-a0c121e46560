package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.ctrip.ibu.platform.shark.sdk.service.l10n.entity.L10nNumberFormatOption;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.Set;

public class CurrencyUtil {

    private static final String rmbCode = "¥";

    private static final Set<String> intCurrency = Set.of("IDR", "TWD", "JPY", "KRW", "VND", "CNY", "SEK");

    public static String getPriceWithCurrency(BigDecimal price, String locale, String currency) {
        if ("zh_cn".equalsIgnoreCase(locale) || "zh-cn".equalsIgnoreCase(locale)) {
            Integer scale = getScale(currency);
            price = price.setScale(scale, RoundingMode.HALF_UP);
            if ("CNY".equalsIgnoreCase(currency)) {
                currency = rmbCode;
            }
            return MessageFormat.format("{0}{1}", currency, price);
        }
        Integer scale = getScale(currency);
        if (scale == 0) {
            return getPriceWithCurrencyWithoutScale(currency, price, locale);
        } else {
            return getPriceWithCurrencyWith2Scale(currency, price, locale);
        }
    }

    public static String getPriceWithCurrencyWithoutScale(String currency, BigDecimal price, String locale) {
        L10nNumberFormatOption l10nNumberFormatOption = new L10nNumberFormatOption();
        l10nNumberFormatOption.setMinimumFractionDigits(0);
        l10nNumberFormatOption.setMaximumFractionDigits(0);
        return L10n.numberFormatter(locale).currencyString(price, currency, l10nNumberFormatOption);
    }

    public static String getPriceWithCurrencyWith2Scale(String currency, BigDecimal price, String locale) {
        L10nNumberFormatOption l10nNumberFormatOption = new L10nNumberFormatOption();
        l10nNumberFormatOption.setMinimumFractionDigits(2);
        l10nNumberFormatOption.setMaximumFractionDigits(2);
        return L10n.numberFormatter(locale).currencyString(price, currency, l10nNumberFormatOption);
    }

    private static Integer getScale(String currency) {
        if (StringUtils.isEmpty(currency)) {
            return 0;
        }
        return intCurrency.contains(currency.toUpperCase()) ? 0 : 2;
    }
}
