package com.ctrip.car.market.coupon.restful.business.seoservice.poi;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache;
import com.ctrip.car.market.coupon.restful.contract.QueryZonesRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType;
import com.ctrip.car.market.coupon.restful.contract.SeoZone;
import com.ctrip.car.market.coupon.restful.contract.seo.PoiDetail;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi.GeoProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.Set;

@Component
public class SeoVendorPoiDetailBusiness extends SeoBaseService<QuerySeoPoiDetailRequestType, QuerySeoPoiDetailResponseType> {

    public SeoVendorPoiDetailBusiness() {
        super(Lists.newArrayList(SeoPage.VENDOR, SeoPage.VENDOR_CITY));
    }

    @Resource
    private SeoService seoService;

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private CarCrossRecommendedServiceProxy carCrossRecommendedServiceProxy;

    @Resource
    private SeoPlatformProxy seoPlatformProxy;

    @Resource
    private GeoProxy geoProxy;

    @Override
    public QuerySeoPoiDetailResponseType doBusiness(QuerySeoPoiDetailRequestType request, SeoPage seoPage) {
        return queryPoiDetail(request);
    }

    public QuerySeoPoiDetailResponseType queryPoiDetail(QuerySeoPoiDetailRequestType request) {
        QuerySeoPoiDetailResponseType response = new QuerySeoPoiDetailResponseType();
        SeoHotVendorDO vendorDO = seoVendorCache.queryVendor(request.getVendorCode());
        if (vendorDO == null) {
            return response;
        }
        SeoHotVendorInformationDO vendorPoi = Optional.ofNullable(request.getCityId()).orElse(0) > 0
                ? seoService.queryVendorHotCityDefault(request.getVendorCode(), request.getCityId())
                : seoService.queryVendorHotCityDefault(request.getVendorCode());

        if (vendorPoi == null) {
            return response;
        }
        PlaceDetailsDTO globalInfo = vendorPoi.getPoiType() == 1 ? null : geoProxy.getPoiDetail(vendorPoi.getPoiCode(), request.getBaseRequest().getLocale());
        //机场
        if (vendorPoi.getPoiType() == 1) {
            setPoi(request, response, vendorPoi, vendorDO.getVendorName());
        } else if (globalInfo != null) {
            setPoi(request, response, globalInfo, vendorDO.getVendorName());
        }

        return response;
    }

    private void setPoi(QuerySeoPoiDetailRequestType request, QuerySeoPoiDetailResponseType response, SeoHotVendorInformationDO vendorInformationDO, String vendorName) {
        QueryZonesRequestType zoneReq = new QueryZonesRequestType();
        zoneReq.setBaseRequest(request.getBaseRequest());
        zoneReq.setCityId(vendorInformationDO.getCityId());
        zoneReq.setAirportCode(vendorInformationDO.getPoiCode());
        QueryZonesResponseType zoneRes = carCrossRecommendedServiceProxy.queryZones(zoneReq);
        if (zoneRes.getZone() != null) {
            SeoZone zone = zoneRes.getZone();
            String airportName = seoPlatformProxy.querySeoName(4, request.getBaseRequest().getLocale(), vendorInformationDO.getPoiCode());
            String countryName = seoPlatformProxy.querySeoName(1, request.getBaseRequest().getLocale(), zone.getCountryId().toString());
            String cityName = seoPlatformProxy.querySeoName(3, request.getBaseRequest().getLocale(), vendorInformationDO.getCityId().toString());
            response.setPoiDetail(new PoiDetail());
            response.getPoiDetail().setCountryId(zone.getCountryId());
            response.getPoiDetail().setCountryName(StringUtils.isNotEmpty(countryName) ? countryName : zone.getCountryName());
            response.getPoiDetail().setCityId(zone.getCityId());
            response.getPoiDetail().setCityName(StringUtils.isNotEmpty(cityName) ? cityName : zone.getCityName());
            response.getPoiDetail().setPoiCode(zone.getAirportCode());
            response.getPoiDetail().setPoiName(StringUtils.isNotEmpty(airportName) ? airportName : zone.getZoneName());
            response.getPoiDetail().setLongitude(zone.getLongitude());
            response.getPoiDetail().setLatitude(zone.getLatitude());
            response.getPoiDetail().setPoiType(vendorInformationDO.getPoiType());
            response.getPoiDetail().setIsHMT(seoService.isHmt(Optional.ofNullable(zone.getProvinceId()).orElse(0)));
            response.getPoiDetail().setVendorName(vendorName);
            response.getPoiDetail().setCountryUrlName(seoService.queryUrlName(Optional.ofNullable(zone.getCountryId()).orElse(0).longValue(), 1));
            response.getPoiDetail().setCityUrlName(seoService.queryUrlName(Optional.ofNullable(zone.getCityId()).orElse(0).longValue(), 2));
            response.getPoiDetail().setPoiTitle(response.getPoiDetail().getPoiName());
        }
    }

    private void setPoi(QuerySeoPoiDetailRequestType request, QuerySeoPoiDetailResponseType response, PlaceDetailsDTO globalInfo, String vendorName) {
        response.setPoiDetail(new PoiDetail());
        response.getPoiDetail().setPoiCode(globalInfo.getCarPlaceId());
        response.getPoiDetail().setPoiName(globalInfo.getName());
        response.getPoiDetail().setLongitude(Optional.ofNullable(globalInfo.getLongitude()).orElse(BigDecimal.ZERO).doubleValue());
        response.getPoiDetail().setLatitude(Optional.ofNullable(globalInfo.getLatitude()).orElse(BigDecimal.ZERO).doubleValue());
        response.getPoiDetail().setCityId(Optional.ofNullable(globalInfo.getCityId()).orElse(0L).intValue());
        City city = seoService.getCity(Optional.ofNullable(globalInfo.getCityId()).orElse(0L).intValue(), request.getBaseRequest().getLocale());
        response.getPoiDetail().setCityName(city == null ? "" : city.getTranslationName());
        response.getPoiDetail().setCountryId(city == null ? 0 : city.getCountryId().intValue());
        response.getPoiDetail().setCountryName(seoService.getCountryName(city == null ? 0 : city.getCountryId().intValue(), request.getBaseRequest().getLocale()));
        response.getPoiDetail().setPoiType(seoService.queryPoiType(globalInfo.getCarPlaceId()));
        response.getPoiDetail().setIsHMT(false);
        response.getPoiDetail().setVendorName(vendorName);
        if (city != null) {
            response.getPoiDetail().setIsHMT(seoService.isHmt(Optional.ofNullable(city.getProvinceId()).orElse(0L).intValue()));
            response.getPoiDetail().setCountryUrlName(seoService.queryUrlName(city.getCountryId(), 1));
            response.getPoiDetail().setCityUrlName(seoService.queryUrlName(city.getId(), 2));
        }
        response.getPoiDetail().setPoiTitle(response.getPoiDetail().getPoiName());
    }
}
