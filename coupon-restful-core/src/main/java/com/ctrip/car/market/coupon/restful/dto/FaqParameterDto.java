package com.ctrip.car.market.coupon.restful.dto;

public class FaqParameterDto {

    //PoiA
    private String poiAName;
    //PoiB
    private String poiBName;
    //城市X
    private String cityXName;
    //经济型车型
    private String smallVehicle;
    //SUV
    private String suvVehicle;
    //豪华型
    private String premiumVehicle;
    //热门车型
    private String lowPriceVehicle;
    //低价
    private String lowPrice;
    //城市top1取车点
    private String top1Poi;

    public String getPoiAName() {
        return poiAName;
    }

    public void setPoiAName(String poiAName) {
        this.poiAName = poiAName;
    }

    public String getPoiBName() {
        return poiBName;
    }

    public void setPoiBName(String poiBName) {
        this.poiBName = poiBName;
    }

    public String getCityXName() {
        return cityXName;
    }

    public void setCityXName(String cityXName) {
        this.cityXName = cityXName;
    }

    public String getSmallVehicle() {
        return smallVehicle;
    }

    public void setSmallVehicle(String smallVehicle) {
        this.smallVehicle = smallVehicle;
    }

    public String getSuvVehicle() {
        return suvVehicle;
    }

    public void setSuvVehicle(String suvVehicle) {
        this.suvVehicle = suvVehicle;
    }

    public String getPremiumVehicle() {
        return premiumVehicle;
    }

    public void setPremiumVehicle(String premiumVehicle) {
        this.premiumVehicle = premiumVehicle;
    }

    public String getLowPriceVehicle() {
        return lowPriceVehicle;
    }

    public void setLowPriceVehicle(String lowPriceVehicle) {
        this.lowPriceVehicle = lowPriceVehicle;
    }

    public String getLowPrice() {
        return lowPrice;
    }

    public void setLowPrice(String lowPrice) {
        this.lowPrice = lowPrice;
    }

    public String getTop1Poi() {
        return top1Poi;
    }

    public void setTop1Poi(String top1Poi) {
        this.top1Poi = top1Poi;
    }
}
