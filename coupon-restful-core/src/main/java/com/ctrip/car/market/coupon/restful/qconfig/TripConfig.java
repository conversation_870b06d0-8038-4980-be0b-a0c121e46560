package com.ctrip.car.market.coupon.restful.qconfig;

import com.ctrip.car.market.coupon.restful.dto.*;
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class TripConfig {

    @QConfig("tripConfig.properties")
    public void onChange(Map<String, String> map) {
        this.pcUrl = map.getOrDefault("pcUrl", "");
        this.productCache = map.getOrDefault("productCache", "0").equals("1");
        this.productCacheExpired = Long.valueOf(map.getOrDefault("productCacheExpired", "60"));
        this.uidCache = map.getOrDefault("uidCache", "0").equals("1");
        this.tmsBuCode = map.getOrDefault("tmsBuCode", "car_calabi");
        this.vehicleGroupKey = map.getOrDefault("vehicleGroupKey", "carcommoditydb|car_model_group|model_group_name_zh|id|");
        this.twCityList = Lists.newArrayList(map.getOrDefault("twCityList", "").split(",")).stream().filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toSet());
        this.maxVehicleCount = Integer.valueOf(map.getOrDefault("maxVehicleCount", "6"));
        this.maxRecommendCityCount = Integer.valueOf(map.getOrDefault("maxRecommendCityCount", "10"));
        this.maxRecommendAirportCount = Integer.valueOf(map.getOrDefault("maxRecommendAirportCount", "3"));
        this.airportLowPriceCacheExpired = Integer.valueOf(map.getOrDefault("airportLowPriceCacheExpired", "60"));
        this.cacheReturn = map.getOrDefault("cacheReturn", "1").equals("1");
        this.carCardCacheExpired = Integer.valueOf(map.getOrDefault("carCardCacheExpired", "1440"));
        this.noDataCacheExpired = Integer.valueOf(map.getOrDefault("noDataCacheExpired", "30"));
        this.noDataCache = map.getOrDefault("noDataCache", "1").equals("1");
    }

    @QConfig("localeFaq.json")
    private List<LocaleSortDto> localeSortList;

    @QConfig("vehicleGroupMap.json")
    private List<KeyValueDto> vehicleGroupMappingList;

    @QConfig("100043032#seoCityPoiConfig.json")
    private List<CityPoiConfigItem> cityPoiConfigList;

    @QConfig("seoVendorLogo.json")
    private List<VendorLogoItem> vendorLogoList;

    @QConfig("seoHotVendorConfig.json")
    private SeoHotVendorDTO seoHotVendor;

    @QConfig("100043032#seoVendorCityPageConfig.json")
    private List<VendorCityDto> vendorCityList;

    @QConfig("100043032#seoPoiConfig.json")
    private List<SeoPoiConfigItem> seoPoiConfigList;

    private String pcUrl;

    private Boolean productCache;

    private Long productCacheExpired;

    private Boolean uidCache;

    private String vehicleGroupKey;

    private String tmsBuCode;

    private Set<Integer> twCityList;

    private Integer maxVehicleCount;

    private Integer maxRecommendCityCount;

    private Integer maxRecommendAirportCount;

    private Integer airportLowPriceCacheExpired;

    private Boolean cacheReturn;

    private Integer carCardCacheExpired;

    private boolean noDataCache;

    private Integer noDataCacheExpired;

    public String getPcUrl() {
        return pcUrl;
    }

    public List<LocaleSortDto> getLocaleSortList() {
        return localeSortList;
    }

    public List<KeyValueDto> getVehicleGroupMappingList() {
        return vehicleGroupMappingList;
    }

    public Long getProductCacheExpired() {
        return productCacheExpired;
    }

    public Boolean getProductCache() {
        return productCache;
    }

    public Boolean getUidCache() {
        return uidCache;
    }

    public String getVehicleGroupKey() {
        return vehicleGroupKey;
    }

    public String getTmsBuCode() {
        return tmsBuCode;
    }

    public Set<Integer> getTwCityList() {
        return twCityList;
    }

    public Integer getMaxVehicleCount() {
        return maxVehicleCount;
    }

    public Integer getMaxRecommendCityCount() {
        return maxRecommendCityCount;
    }

    public Integer getMaxRecommendAirportCount() {
        return maxRecommendAirportCount;
    }

    public Integer getAirportLowPriceCacheExpired() {
        return airportLowPriceCacheExpired;
    }

    public Boolean getCacheReturn() {
        return cacheReturn;
    }

    public List<CityPoiConfigItem> getCityPoiConfigList() {
        return cityPoiConfigList;
    }

    public Integer getCarCardCacheExpired() {
        return carCardCacheExpired;
    }

    public List<VendorLogoItem> getVendorLogoList() {
        return vendorLogoList;
    }

    public SeoHotVendorDTO getSeoHotVendor() {
        return seoHotVendor;
    }

    public Integer getNoDataCacheExpired() {
        return noDataCacheExpired;
    }

    public boolean getNoDataCache() {
        return noDataCache;
    }

    public List<VendorCityDto> getVendorCityList() {
        return vendorCityList;
    }

    public List<SeoPoiConfigItem> getSeoPoiConfigList() {
        return seoPoiConfigList;
    }
}