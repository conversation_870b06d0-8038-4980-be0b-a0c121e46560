package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.common.utils.ABTestUtils;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.MemberPrivilegeConfigDTO;
import com.ctrip.car.market.coupon.restful.dto.UserMktConditionDTO;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseCodeUtils;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import soa.ctrip.com.tour.ai.user.label.LabelInfo;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class UserLableCodeBusiness {


    @Autowired
    UserLabelAiProxy userLabelAiProxy;

    @Autowired
    CouponServiceQConfig serviceQConfig;
    private final ILog logger = LogManager.getLogger(UserLableCodeBusiness.class);

    public queryUserLabelCodeResponseType queryUserLabelCode(queryUserLabelCodeRequestType request) {
        queryUserLabelCodeResponseType responseType = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, queryUserLabelCodeResponseType.class);
        String uid = BaseUtils.getUidByCommon(request.getHead());
        responseType.setLabelResult(false);
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            uid = BaseUtils.getQunarUidByCommon(request.getRestRequestHeader());
        }
        if (StringUtils.isBlank(uid)) {
            return ResponseCodeUtils.res(ResultOpenStatus.NO_LOGIN, queryUserLabelCodeResponseType.class);
        }
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            uid = "Q_" + uid;
        }
        if (CollectionUtils.isEmpty(request.getLabelCodes())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserLabelCodeResponseType.class);

        }
        List<LabelInfo> result = userLabelAiProxy.queryUserLabelCode(uid, request.getLabelCodes());
        if (CollectionUtils.isNotEmpty(result)) {
            responseType.setUserLabelInfos(result.stream().map(x -> {
                        UserLabelInfo userLabelInfo = new UserLabelInfo();
                        userLabelInfo.setLabelCode(x.getLabelId());
                        userLabelInfo.setLabelValue(x.getLabelValue());
                        return userLabelInfo;
                    }).collect(Collectors.toList())
            );
            responseType.setLabelResult(result.stream().allMatch(x -> "true".equalsIgnoreCase(x.getLabelValue())));
        }
        return responseType;

    }

    public queryUserMktConditionResponseType queryUserMktCondition(queryUserMktConditionRequestType request) throws Exception {
        queryUserMktConditionResponseType responseType = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, queryUserMktConditionResponseType.class);
        if (CollectionUtils.isEmpty(request.getLabelCodes()) || StringUtils.isEmpty(request.getScenecode()) || StringUtils.isEmpty(request.getSourcefrom())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserMktConditionResponseType.class);

        }
        if (isMiniAPP(request.getSourcefrom()) && request.getMiniType() == null) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserMktConditionResponseType.class);

        }
        if (StringUtils.isEmpty(request.getCid()) && StringUtils.isEmpty(request.getHead().getCid())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserMktConditionResponseType.class);
        }

        boolean isNewUser = false;
        String uid = BaseUtils.getUidByCommon(request.getHead());
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            uid = BaseUtils.getQunarUidByCommon(request.getRestRequestHeader());
        }
        if (!StringUtils.isBlank(uid)) {

            if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
                uid = "Q_" + uid;
            }
            List<LabelInfo> result = userLabelAiProxy.queryUserLabelCode(uid, request.getLabelCodes());
            if (CollectionUtils.isNotEmpty(result)) {
                responseType.setUserLabelInfos(result.stream().map(x -> {
                            UserLabelInfo userLabelInfo = new UserLabelInfo();
                            userLabelInfo.setLabelCode(x.getLabelId());
                            userLabelInfo.setLabelValue(x.getLabelValue());
                            return userLabelInfo;
                        }).collect(Collectors.toList())
                );
                isNewUser = result.stream().allMatch(x -> "true".equalsIgnoreCase(x.getLabelValue()));
            }

        }
        Map<String, String> tags = new HashMap<>();
        tags.put("uid", uid);
        tags.put("cid", request.getCid());
        tags.put("scenecode", request.getScenecode());
        tags.put("sourceFrom", request.getSourcefrom());
        tags.put("isNewUser", isNewUser + "");
        responseType.setLabelResult(isNewUser);

        // 裂变实验
        String shareTestingVersion = ABTestUtils.getAB(serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber"), Optional.ofNullable(request.getHead().getCid()).orElse(request.getCid()));
        // 企微实验
        String wechatTestingVersion = ABTestUtils.getAB(serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber"), Optional.ofNullable(request.getHead().getCid()).orElse(request.getCid()));
        // 是否是小程序
        int miniType = isCAPP(request.getSourcefrom()) ? 0 : request.getMiniType();
        // 老客or新客
        String newOrOldVersion = (isNewUser && org.apache.commons.lang3.StringUtils.isNotBlank(uid)) ? "N" : "O";

        // banner
        if (isMidBanner(request.getScenecode())) {
            responseType.setInfos(buildResultNew(request.getSourcefrom(), request.getScenecode(), shareTestingVersion, wechatTestingVersion, newOrOldVersion, miniType));
            if (CollectionUtils.isEmpty(responseType.getInfos()) || responseType.getInfos().stream().anyMatch(x -> StringUtils.isEmpty(x.getJumpUrl()) || StringUtils.isEmpty(x.getImageUrl()))) {
                responseType.setInfos(buildResultNew(request.getSourcefrom(), request.getScenecode(), "default", "default", "default", miniType));
            }
        }

        // 弹框
        if (isJumpBanner(request.getScenecode())) {
            responseType.setInfos(buildResultNew(request.getSourcefrom(), request.getScenecode(), shareTestingVersion, wechatTestingVersion, newOrOldVersion, miniType));
        }

        Metrics.build().withTag("queryUserMktCondition", "middleBanner")
                .withTag("isNewUser",isNewUser+"")
                .withTag("shareTestingVersion",shareTestingVersion)
                .withTag("wechatTestingVersion",wechatTestingVersion)
                .withTag("result", shareTestingVersion + wechatTestingVersion + isNewUser).recordOne(MetricsEnum.MKT_USER.getTitle());

        logger.warn("queryUserMktCondition",  "shareAbTestResult:" + shareTestingVersion + "; wechatBTestResult:" + wechatTestingVersion + ";"
                + "newOrOldVersion:" + newOrOldVersion + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);
        return responseType;
    }


    private List<JumpImageInfo> buildResultNew(String sourceFrom, String sceneCode, String shareTestingVersion, String wechatTestingVersion, String newOrOldVersion, Integer miniType) {
        List<UserMktConditionDTO> mktConditionDTOS = getUserMktConditionDTONew(sourceFrom, sceneCode, shareTestingVersion, wechatTestingVersion, newOrOldVersion, miniType);
        if (CollectionUtils.isEmpty(mktConditionDTOS)) {
            return  new ArrayList<>();
        }
        return (mktConditionDTOS.stream().map(x -> {
            JumpImageInfo jumpImageInfo = new JumpImageInfo();
            jumpImageInfo.setImageUrl(x.getImageUrl());
            jumpImageInfo.setJumpUrl(x.getJumpUrl());
            jumpImageInfo.setDisplayFrequency(Long.parseLong(x.getDisplay()));
            return jumpImageInfo;
        }).collect(Collectors.toList()));
    }

    private List<UserMktConditionDTO> getUserMktConditionDTONew(String sourceFrom, String sceneCode, String shareTestingVersion, String wechatTestingVersion, String newOrOldVersion, Integer miniType) {
        List<UserMktConditionDTO> userMktConditions3 = serviceQConfig.getUserMktConditions3();
        if (CollectionUtils.isEmpty(userMktConditions3)) {
            return  new ArrayList<>();
        }
        return userMktConditions3.stream()
                .filter(x -> x.getSceneCode().equalsIgnoreCase(sceneCode))
                .filter(x -> x.getSourceFrom().equals(sourceFrom))
                .filter(x -> {
                    boolean valid = true;
                    // 检查 shareTestingVersion
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(x.getShareTestingVersion())) {
                        List<String> shareVersions = Arrays.asList(x.getShareTestingVersion().split(","));
                        valid = shareVersions.contains(shareTestingVersion);
                    }
                    // 检查 wechatTestingVersion
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(x.getWechatTestingVersion())) {
                        List<String> wechatVersions = Arrays.asList(x.getWechatTestingVersion().split(","));
                        valid = valid && wechatVersions.contains(wechatTestingVersion);
                    }
                    // 检查 newOrOldVersion
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(x.getNewOrOldVersion())) {
                        List<String> newOrOldVersions = Arrays.asList(x.getNewOrOldVersion().split(","));
                        valid = valid && newOrOldVersions.contains(newOrOldVersion);
                    }
                    return valid;
                })
                .filter(UserMktConditionDTO::getUseABTest)
                .filter(x -> x.getMiniType().equals(Optional.ofNullable(miniType).orElse(0)))
                .collect(Collectors.toList());
    }


    private List<JumpImageInfo> buildResult(String sourceFrom, String sceneCode, String abResult, Integer miniType) {
        List<UserMktConditionDTO> mktConditionDTOS = getUserMktConditionDTO(sourceFrom, sceneCode, abResult, miniType);
        return (mktConditionDTOS.stream().map(x -> {
            JumpImageInfo jumpImageInfo = new JumpImageInfo();
            jumpImageInfo.setImageUrl(x.getImageUrl());
            jumpImageInfo.setJumpUrl(x.getJumpUrl());
            jumpImageInfo.setDisplayFrequency(Long.parseLong(x.getDisplay()));
            return jumpImageInfo;
        }).collect(Collectors.toList()));
    }

    private List<UserMktConditionDTO> getUserMktConditionDTO(String sourceFrom, String sceneCode, String abResult, Integer miniType) {
        return serviceQConfig.getUserMktConditions2().stream().filter(x -> x.getSceneCode().equalsIgnoreCase(sceneCode)).filter(x -> x.getSourceFrom().equals(sourceFrom)).filter(
                        x -> x.getAbResult().equalsIgnoreCase(abResult)
                ).filter(UserMktConditionDTO::getUseABTest)
                .filter(x -> x.getMiniType().equals(Optional.ofNullable(miniType).orElse(0)))
                .collect(Collectors.toList());
    }

    /**
     * APP判断
     *
     * @param sourceFrom
     * @return
     */
    private boolean isCAPP(String sourceFrom) {
        return serviceQConfig.findAPPSourceFrom(sourceFrom);

    }

    /***
     * 微信小程序判断
     * @param sourceFrom
     * @return
     */
    private boolean isMiniAPP(String sourceFrom) {
        return serviceQConfig.findWeChatSourceFrom(sourceFrom);

    }

    /***
     * 中部banner 判断
     * @param sceneCode
     * @return
     */
    private boolean isMidBanner(String sceneCode) {
        return (serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner").equalsIgnoreCase(sceneCode));

    }

    /***
     * 弹框判断
     * @param sceneCode
     * @return
     */
    private boolean isJumpBanner(String sceneCode) {
        return (serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner").equalsIgnoreCase(sceneCode));

    }


}
