package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoFaqService {

    @Resource
    private List<ISeoService<QuerySeoFaqRequestType, QuerySeoFaqResponseType>> seoServiceList;

    public QuerySeoFaqResponseType queryFaq(QuerySeoFaqRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), request.getVendorCode());
        if (seoPage == null) {
            return new QuerySeoFaqResponseType();
        }
        ISeoService<QuerySeoFaqRequestType, QuerySeoFaqResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoFaqResponseType();
        }
        return service.query(request, seoPage);
    }
}
