package com.ctrip.car.market.coupon.restful.business.seoservice.information;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationResponseType;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SeoProvinceInformationBusiness extends SeoBaseService<QuerySeoInformationRequestType, QuerySeoInformationResponseType> {

    public SeoProvinceInformationBusiness() {
        super(Lists.newArrayList(SeoPage.PROVINCE, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Resource
    private SeoService seoService;

    @Resource
    private SeoCityInformationBusiness seoCityInformationBusiness;

    @Override
    public QuerySeoInformationResponseType doBusiness(QuerySeoInformationRequestType request, SeoPage seoPage) {
        QuerySeoInformationResponseType response = new QuerySeoInformationResponseType();
        SeoProvinceQueryParameter queryParameter = seoService.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
        if (queryParameter == null || queryParameter.getQueryParameter() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        QuerySeoInformationRequestType cityRequest = new QuerySeoInformationRequestType();
        cityRequest.setBaseRequest(request.getBaseRequest());
        cityRequest.setCityId(queryParameter.getQueryParameter().getCityId());
        return seoCityInformationBusiness.queryInformation(cityRequest);
    }
}
