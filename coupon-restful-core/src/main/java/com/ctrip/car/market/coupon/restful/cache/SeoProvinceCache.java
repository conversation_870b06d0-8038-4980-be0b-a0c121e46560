package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.seo.SeoHotProvinceinfoDO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@CreateCacheBean
public class SeoProvinceCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoProvinceCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_PROVINCE_LEY)
    public Cache<Integer, List<SeoHotProvinceinfoDO>> provinceCache;

    public SeoHotProvinceinfoDO queryByProvince(Integer provinceId) {
        List<SeoHotProvinceinfoDO> values = provinceCache.get(provinceId);
        return CollectionUtils.isNotEmpty(values) ? values.get(0) : null;
    }
}
