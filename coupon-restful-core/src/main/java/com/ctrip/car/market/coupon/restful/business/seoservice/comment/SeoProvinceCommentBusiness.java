package com.ctrip.car.market.coupon.restful.business.seoservice.comment;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.contract.seo.CommentInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListResponseType;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.ListCommentsResponseType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class SeoProvinceCommentBusiness extends SeoBaseService<QuerySeoCommentListRequestType, QuerySeoCommentListResponseType> {

    public SeoProvinceCommentBusiness() {
        super(Lists.newArrayList(SeoPage.PROVINCE, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Resource
    private SeoService seoService;

    @Resource
    private UcpServiceProxy ucpServiceProxy;

    @Override
    public QuerySeoCommentListResponseType doBusiness(QuerySeoCommentListRequestType request, SeoPage seoPage) {
        QuerySeoCommentListResponseType response = new QuerySeoCommentListResponseType();
        SeoProvinceQueryParameter queryParameter = seoService.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
        if (queryParameter == null || queryParameter.getQueryParameter() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        String locale = StringUtils.isNotEmpty(request.getBaseRequest().getLocale()) ? request.getBaseRequest().getLocale() : "en-US";
        ListCommentsResponseType commentsResponse = ucpServiceProxy.queryComment(locale, null, queryParameter.getQueryParameter().getCityId());
        response.setTotalCount(0);
        if (commentsResponse != null) {
            response.setCommentList(commentsResponse.getComments().stream().map(l -> {
                CommentInfo info = new CommentInfo();
                info.setAvatarUrl(l.getUserInfo().getAvatarUrl());
                info.setUserDisplayName(l.getUserInfo().getDisplayName());
                info.setContent(l.getContent());
                info.setTranslatedContent(l.getTranslatedContent());
                info.setSocre(l.getScore());
                info.setCommentTime(seoService.commentDateFormat(l.getCommentTime(), locale));
                info.setVehicleName(Optional.ofNullable(l.getExtInfoMap()).orElse(Maps.newHashMap()).get("vehicleName"));
                return info;
            }).collect(Collectors.toList()));
            response.setTotalCount(commentsResponse.getTotalCount());
        }
        if (queryParameter.getSeoPage() == SeoPage.PROVINCE) {
            response.setTitle(LanguageUtils.sharkValFormat(SeoShark.RentalCommentTitle.getValue(locale), seoService.getProvinceName(queryParameter.getQueryParameter().getPoiA().getPoiId().intValue(), locale)));
        } else {
            String poiName = queryParameter.getSeoPage() == SeoPage.STATION
                    ? seoService.getGlobalPoiName(queryParameter.getQueryParameter().getPoiA().getPoiId(), locale)
                    : seoService.queryPoiName(2, queryParameter.getQueryParameter().getPoiA().getPoiId().toString(), locale);
            response.setTitle(LanguageUtils.sharkValFormat(SeoShark.RentalCommentTitle.getValue(locale), poiName, locale));
        }
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }
}
