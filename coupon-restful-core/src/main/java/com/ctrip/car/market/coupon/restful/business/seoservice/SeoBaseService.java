package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.enums.SeoPage;

import java.util.List;

public abstract class SeoBaseService<T, R> implements ISeoService<T, R> {

    private final List<SeoPage> supportPage;

    protected SeoBaseService(List<SeoPage> supportPage) {
        this.supportPage = supportPage;
    }

    @Override
    public boolean isSupport(SeoPage seoPage) {
        return this.supportPage.contains(seoPage);
    }

    @Override
    public R query(T request, SeoPage seoPage) {
        return doBusiness(request, seoPage);
    }

    public abstract R doBusiness(T request, SeoPage seoPage);
}
