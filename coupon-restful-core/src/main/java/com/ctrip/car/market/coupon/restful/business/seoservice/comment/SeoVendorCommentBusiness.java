package com.ctrip.car.market.coupon.restful.business.seoservice.comment;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache;
import com.ctrip.car.market.coupon.restful.contract.seo.*;
import com.ctrip.car.market.coupon.restful.dto.SubItemConfigDto;
import com.ctrip.car.market.coupon.restful.dto.VendorLogoItem;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.CarMarketReadCache;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import com.ctrip.car.osd.framework.common.utils.JsonUtil;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.CommonQueryCommentSummaryResponseType;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CommentDetailInfoType;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class SeoVendorCommentBusiness extends SeoBaseService<QuerySeoCommentListRequestType, QuerySeoCommentListResponseType> {

    public SeoVendorCommentBusiness() {
        super(Lists.newArrayList(SeoPage.VENDOR, SeoPage.VENDOR_CITY));
    }

    @Resource
    private UcpServiceProxy ucpServiceProxy;

    @Resource
    private SeoService seoService;

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private TripConfig tripConfig;

    @Override
    public QuerySeoCommentListResponseType doBusiness(QuerySeoCommentListRequestType request, SeoPage seoPage) {
        return queryComment(request);
    }

    public QuerySeoCommentListResponseType queryComment(QuerySeoCommentListRequestType request) {
        QuerySeoCommentListResponseType response = new QuerySeoCommentListResponseType();
        SeoHotVendorDO vendorDO = seoVendorCache.queryVendor(request.getVendorCode());
        if (vendorDO == null) {
            return response;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
            return cityPage(request, vendorDO);
        }
        return vendorPage(request, vendorDO);
    }

    public QuerySeoCommentListResponseType vendorPage(QuerySeoCommentListRequestType request, SeoHotVendorDO vendorDO) {
        QuerySeoCommentListResponseType response = new QuerySeoCommentListResponseType();
        //查询供应商top3热门城市
        List<SeoHotVendorInformationDO> vendorCityList = seoService.queryVendorTop3City(request.getVendorCode());
        List<CommentDetailInfoType> commentList = Lists.newArrayList();
        for (SeoHotVendorInformationDO vendorCity : vendorCityList) {
            CommonQueryCommentSummaryResponseType responseType = ucpServiceProxy.queryVendorCityComment(request.getBaseRequest().getLocale(), request.getVendorCode(), vendorCity.getCityId());
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getComments())) {
                commentList.addAll(responseType.getComments());
            }
            if (commentList.size() >= 8) {
                break;
            }
        }
        commentList = commentList.stream().limit(8).collect(Collectors.toList());
        //点评列表
        response.setCommentList(commentConvert(commentList, request.getBaseRequest().getLocale()));
        //标题
        response.setTitle(LanguageUtils.sharkValFormat(SeoShark.RentalCommentTitle.getValue(request.getBaseRequest().getLocale()), vendorDO.getVendorName()));
        //细项评分
        response.setVendorComment(new VendorCommentInfo());
        SeoVendorCommentScoreDO vendorCommentScoreDO = seoVendorCache.queryVendorCommentScore(request.getVendorCode());
        if (vendorCommentScoreDO != null) {
            List<SubItemConfigDto> subItemList = JsonUtil.getObjList(vendorCommentScoreDO.getSubItemScore(), SubItemConfigDto.class);
            //点评总数
            response.setTotalCount(Optional.ofNullable(vendorCommentScoreDO.getTotalCount()).orElse(0));
            response.getVendorComment().setScore(scoreFormat(vendorCommentScoreDO.getSocre()));
            response.getVendorComment().setVendorScoreList(seoService.dbSubItemConvert(subItemList, request.getBaseRequest().getLocale()));
            response.getVendorComment().setLogo(getVendorLogo(request.getVendorCode()));
        }
        return response;
    }

    public QuerySeoCommentListResponseType cityPage(QuerySeoCommentListRequestType request, SeoHotVendorDO vendorDO) {

        QuerySeoCommentListResponseType response = new QuerySeoCommentListResponseType();
        CommonQueryCommentSummaryResponseType responseType = ucpServiceProxy.queryVendorCityComment(request.getBaseRequest().getLocale(), request.getVendorCode(), request.getCityId());
        //点评列表
        response.setCommentList(commentConvert(responseType.getComments(), request.getBaseRequest().getLocale()));
        response.setTotalCount(0);
        //细项评分
        response.setVendorComment(new VendorCommentInfo());
        if (responseType.getCommentAggregation() != null) {
            //点评总数
            response.setTotalCount(Optional.ofNullable(responseType.getCommentAggregation().getTotalCount()).orElse(0));
            response.getVendorComment().setScore(scoreFormat(responseType.getCommentAggregation().getScoreAvg()));
            response.getVendorComment().setVendorScoreList(seoService.apiSubItemConvert(responseType.getCommentAggregation().getSubItemTags(), request.getBaseRequest().getLocale()));
            response.getVendorComment().setLogo(getVendorLogo(request.getVendorCode()));
        }
        //标题
        response.setTitle(LanguageUtils.sharkValFormat(SeoShark.RentalCommentTitle.getValue(request.getBaseRequest().getLocale()), vendorDO.getVendorName()));
        return response;
    }

    private List<CommentInfo> commentConvert(List<CommentDetailInfoType> list, String locale) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(l -> {
            CommentInfo info = new CommentInfo();
            info.setAvatarUrl(l.getUserInfo().getAvatarUrl());
            info.setUserDisplayName(l.getUserInfo().getDisplayName());
            info.setContent(l.getContent());
            info.setTranslatedContent(l.getTranslatedContent());
            info.setSocre(scoreFormat(l.getScore()));
            info.setCommentTime(seoService.commentDateFormat(l.getCommentTime(), locale));
            info.setVehicleName(l.getExtInfoMap() != null ? l.getExtInfoMap().get("vehicleName") : null);
            return info;
        }).collect(Collectors.toList());
    }

    private String getVendorLogo(String vendorCode) {
        VendorLogoItem logoItem = tripConfig.getVendorLogoList().stream().filter(li -> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(li.getVendorCode(), vendorCode.trim())).findFirst().orElse(null);
        if (logoItem != null) {
            return logoItem.getLogo();
        }
        return CarMarketReadCache.get("car.market.seo.vendor.logo." + vendorCode.toLowerCase());
    }

    private BigDecimal scoreFormat(BigDecimal score) {
        return score == null ? null : score.setScale(1, RoundingMode.UP);
    }
}
