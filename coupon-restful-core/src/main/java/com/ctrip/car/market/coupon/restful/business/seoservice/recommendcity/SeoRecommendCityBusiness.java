package com.ctrip.car.market.coupon.restful.business.seoservice.recommendcity;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.RecommendCityInfo;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.dcs.geo.domain.value.City;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SeoRecommendCityBusiness extends SeoBaseService<QuerySeoRecommendCityRequestType, QuerySeoRecommendCityResponseType> {

    public SeoRecommendCityBusiness() {
        super(Lists.newArrayList(SeoPage.COUNTRY, SeoPage.AIRPORT));
    }

    @Resource
    private SeoService service;

    @Resource
    private TripConfig tripConfig;

    @Override
    public QuerySeoRecommendCityResponseType doBusiness(QuerySeoRecommendCityRequestType request, SeoPage seoPage) {
        return queryCity(request);
    }

    public QuerySeoRecommendCityResponseType queryCity(QuerySeoRecommendCityRequestType request) {
        QuerySeoRecommendCityResponseType response = new QuerySeoRecommendCityResponseType();
        if (request.getBaseRequest() == null) {
            response.setBaseResponse(ResponseUtil.fail("baseRequest is null"));
            return response;
        }
        if (Optional.ofNullable(request.getCountryId()).orElse(0) <= 0
                && StringUtils.isEmpty(request.getPoiCode())) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        if (request.getPoiType() == null && StringUtils.isNotEmpty(request.poiCode)) {
            request.setPoiType(1);
        }
        List<SeoHotDestinatioinfoDO> destinatioinfoList = service.queryHotDestination(request.getCountryId(), request.getCityId(), request.getPoiType(), request.getPoiCode());
        if (CollectionUtils.isEmpty(destinatioinfoList)) {
            response.setBaseResponse(ResponseUtil.fail("destination error"));
            return response;
        }
        Integer countryId = destinatioinfoList.get(0).getCountryId();
        String locale = request.getBaseRequest().getLocale();
        List<SeoHotDestinatioinfoDO> list = Lists.newArrayList();
        boolean isAirportPage = false;
        //机场
        if ((Optional.ofNullable(request.getPoiType()).orElse(0) > 0 && StringUtils.isNotEmpty(request.getPoiCode()))) {
            list = service.queryHotDestination(countryId, null, null, null);
            isAirportPage = true;
        } else {
            list = destinatioinfoList;
        }
        List<SeoHotCityinfoDO> cityList = service.queryHotCity(countryId);
        if (CollectionUtils.isEmpty(cityList)) {
            return response;
        }
        String countryName = service.getCountryName(countryId, locale);
        //台湾站特殊处理
        if ("zh-tw".equalsIgnoreCase(locale) && Objects.equals(countryId, 1)) {
            if (StringUtils.isNotEmpty(request.getPoiCode())) {
                SeoHotDestinatioinfoDO destinatioinfoDO = list.stream().filter(l -> StringUtils.equalsIgnoreCase(l.getPoiCode(), request.getPoiCode())).findFirst().orElse(null);
                City cityInfo = destinatioinfoDO != null ? service.getCity(destinatioinfoDO.getCityId()) : null;
                //台湾的城市页面
                if (cityInfo != null && Objects.equals(cityInfo.getProvinceId(), 53L)) {
                    countryName = service.getProvinceName(53, locale);
                    cityList = cityList.stream().filter(l -> tripConfig.getTwCityList().contains(l.getCityId())).collect(Collectors.toList());
                } else {
                    //排除台湾的城市
                    cityList = cityList.stream().filter(l -> !tripConfig.getTwCityList().contains(l.getCityId())).collect(Collectors.toList());
                }
            } else {
                //排除台湾的城市
                cityList = cityList.stream().filter(l -> !tripConfig.getTwCityList().contains(l.getCityId())).collect(Collectors.toList());
            }
        }
        response.setCityList(convert(cityList, list, locale));
        response.setTitle(LanguageUtils.sharkValFormat(isAirportPage ? SeoShark.RecommendCityAirportTitle.getValue(locale) : SeoShark.RecommendCityTitle.getValue(locale), countryName));
        response.setSubTitle(LanguageUtils.sharkValFormat(isAirportPage ? SeoShark.RecommendCityAirportSubTitle.getValue(locale) : SeoShark.RecommendCitySubTitle.getValue(locale), countryName));
        response.setBaseResponse(ResponseUtil.success());
        Metrics.build().withTag("country", Optional.ofNullable(request.getCountryId()).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getCityList()) ? "1" : "0")
                .recordOne(MetricsEnum.SEO_RECOMMEND_CITY.getTitle());
        return response;
    }

    private List<RecommendCityInfo> convert(List<SeoHotCityinfoDO> cityList, List<SeoHotDestinatioinfoDO> destanationList, String locale) {
        Map<Integer, Long> cityOrderMap = service.getCityOrderNum(destanationList);
        List<SeoHotCityinfoDO> citys = cityList.stream().sorted(Comparator.comparing(l -> cityOrderMap.getOrDefault(l.getCityId(), 999L))).limit(tripConfig.getMaxRecommendCityCount()).collect(Collectors.toList());
        Map<Long, City> cityMap = service.getCity(citys.stream().map(l -> l.getCityId().longValue()).distinct().collect(Collectors.toList()), locale);
        return citys.stream().map(l -> {
            if (StringUtils.isEmpty(l.getImage())) {
                return null;
            }
            RecommendCityInfo item = new RecommendCityInfo();
            item.setCityName(cityMap.get(l.getCityId().longValue()).getTranslationName());
            item.setUrl(service.getSiteUrl(l.getUrl(), locale));
            item.setImgUrl(l.getImage());
            return item;
        }).filter(Objects::nonNull).limit(4).collect(Collectors.toList());
    }
}
