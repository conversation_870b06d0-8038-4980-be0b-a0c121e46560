package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class SeoPoiCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoPoiCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_POI_KEY)
    public Cache<String, List<SeoHotDestinatioinfoDO>> poiCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoPoiCityCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_POI_CITY_KEY)
    public Cache<Integer, List<SeoHotDestinatioinfoDO>> cityPoiCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoPoiCountryCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_POI_COUNTRY_KEY)
    public Cache<Integer, List<SeoHotDestinatioinfoDO>> countryPoiCache;


    public List<SeoHotDestinatioinfoDO> queryByPoiCode(Integer poiType, String poiCode) {
        if (StringUtils.isEmpty(poiCode)) {
            return null;
        }
        return poiCache.get(poiType + "-" + poiCode.toLowerCase());
    }

    public List<SeoHotDestinatioinfoDO> queryByCity(Integer cityId) {
        return cityPoiCache.get(cityId);
    }

    public List<SeoHotDestinatioinfoDO> queryByCountry(Integer countryId) {
        return countryPoiCache.get(countryId);
    }

    public SeoHotDestinatioinfoDO queryTop1Airport(List<Integer> cityIdList) {
        if (CollectionUtils.isEmpty(cityIdList)) {
            return null;
        }
        List<SeoHotDestinatioinfoDO> list = cityIdList.stream().map(this::queryByCity).filter(l -> l != null && !l.isEmpty()).flatMap(List::stream).toList();
        return list.stream().filter(l -> l.getPoiType() != null && l.getPoiType() == 1).max(Comparator.comparing(SeoHotDestinatioinfoDO::getOrderNum)).orElse(null);
    }

    public SeoHotDestinatioinfoDO queryTop1Airport(Integer countryId) {
        if (countryId == null) {
            return null;
        }
        return queryByCountry(countryId).stream().filter(l -> l.getPoiType() != null && l.getPoiType() == 1).max(Comparator.comparing(SeoHotDestinatioinfoDO::getOrderNum)).orElse(null);
    }
}
