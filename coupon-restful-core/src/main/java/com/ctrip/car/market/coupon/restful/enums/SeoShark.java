package com.ctrip.car.market.coupon.restful.enums;

import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import org.apache.commons.lang3.StringUtils;

public enum SeoShark {

    City("city", "Cities in {0}"),
    Airport("airport", "Airport in {0}"),
    CarRental("carrental", "Car rentals in {0}"),
    HotCompany("hot.company", "Most popular car rental company"),
    HotVehicleGroup("hot.vehicle.group", "The most popular to book"),
    HotDailyPrice("hot.daily.price", "Average daily price"),
    HotRentalTerm("hot.rental.term", "Typical rental term"),
    HotRentalTermValue("hot.rental.term.value", "{0} days"),
    HotRentalTermPluralValue("hot.rental.term.value.pluralsuffix.other", "{0} days"),
    RentalCommentTitle("rental.comment.title", "{0} Car rental user reviews"),
    RecommendCityTitle("recommend.city.title", "{0} Recommended Popular Car Rental Cities"),
    RecommendCitySubTitle("recommend.airport.sub.title", "Check which cities in {0} you can depart from. You can usually find discounted prices when picking up your car at the airport"),
    RecommendAirportTitle("recommend.airport.title", "{0} Popular Airport Car Rental Recommendations"),
    RecommendAirportSubTitle("recommend.city.sub.title", "Check which cities in {0} you can depart from. You can usually find discounted prices when picking up your car at the airport"),
    RecommendAirportItemTitle("recommend.airport.item.title", "Car rental service at {0}"),

    RecommendCityAirportTitle("recommend.city.airport.title", "Recommended Popular Car Rental Locations in the {0}"),
    RecommendCityAirportSubTitle("recommend.city.airport.sub.title", "We recommend the most popular cities to rent a car in the {0}, so you can get to where you want to go at any time with an easy drive!"),

    Comment_Item_Car("comment.item.car", ""),
    Comment_Item_Clear("comment.item.clear", ""),
    Comment_Item_Pick("comment.item.pick", ""),
    Comment_Item_Service("comment.item.service", ""),


    FaqQ("faq.q", ""),
    FaqA("faq.a", ""),

    VendorFaqQ("vendor.faq.q", ""),
    VendorFaqA("vendor.faq.a", ""),

    VendorCityFaqQ("vendor.city.faq.q",""),
    VendorCityFaqA("vendor.city.faq.a",""),

    ProvinceFaqQ("province.faq.q",""),
    ProvinceFaqA("province.faq.a",""),

    StationFaqQ("station.faq.q",""),
    StationFaqA("station.faq.a",""),

    SceneryFaqQ("scenery.faq.q",""),
    SceneryFaqA("scenery.faq.a",""),

    FaqQ1("faq.q1", "What is the lowest rental price for renting a car at {0}?"),
    FaqA1("faq.a1", "The minimum rent for car rental in {0} is {1}. Please obtain the latest low price through inquiry."),
    FaqQ2("faq.q2", "Can I pick up and return the car at different locations on {0}?"),
    FaqA2("faq.a2", "To return the car in different locations, simply check the box for 'Return to another location' and select your 'Return location'. We will display all the results of one-way cross regional car returns found in the search."),
    FaqQ3("faq.q3", "What documents and information are required when renting a car from {0}?"),
    FaqA3("faq.a3", "Depending on the car rental company you are booking with and the country/region you are traveling in, the rental provider may require certain documents (a valid driver's license, your passport, credit card, and other documents with your name and address on them) and print the booking voucher. Our best recommendation is to consult your car rental provider and the local embassy or consulate of the destination country/region (or visit their website) before making a reservation. Remember, all passengers have a responsibility to prepare the correct documents before traveling.\n" +
            "Important reminder: When picking up the car from the store, all additional driver and equipment fees must be paid. Please consider these fees when using a credit card. If a valid credit card cannot be presented, or if the available credit limit on the credit card is insufficient, or if the credit card is not in the name of the driver, the staff may refuse to pick up the car. In this case, refunds are not possible."),
    FaqQ4("faq.q4", "Can I still rent a car without a credit card?"),
    FaqA4("faq.a4", "In the vast majority of cases, the driver needs to provide a credit card when picking up the car - although some rental companies also accept debit cards.\n" + "However, regardless of the method used, the name of the driver must be on the card and there must be sufficient available credit to pay the vehicle deposit."),
    FaqQ5("faq.q5", "How to choose a car model suitable for {0} driving?"),
    FaqA5_Small("faq.a5.small", "For driving {0}, it is recommended to choose to rent a small sedan or compact car, as these models are more flexible on narrow urban roads and parking is also more convenient."),
    FaqA5_MediumLarge("faq.a5.medium-large", "When driving in {0}, it is recommended to rent medium to large-sized sedan models, which usually have comfortable seating and storage space, suitable for long-term city driving and carrying items."),
    FaqA5_Premium("faq.a5.premium", "For {0} driving, you can choose to rent a premium sedan model, which has advantages such as comfort, technological features, powerful power, advanced safety, and brand image."),
    FaqA5_SUV("faq.a5.suv", "When driving on {0}, you can choose to rent SUV models that have powerful power and off-road capabilities, adapting to various road conditions."),
    FaqA5_VAN("faq.a5.van", "Driving in {0}, you can choose to rent a business car model, which has spacious interior space, luxurious seats, and advanced facilities, providing a comfortable and convenient riding experience."),
    FaqA5_CoupeCabrio("faq.a5.coupe-cabrio", "For {0} driving, you can choose to rent a sports car/sedan model, where you will experience powerful power and exciting acceleration performance, allowing you to feel the charm of speed."),
    FaqA5_Pickup("faq.a5.pickup", "Driving on {0}, you can choose to rent a truck model, which provides spacious cargo space for easy handling of large items and goods, meeting your needs."),
    FaqA5_HighEnd("faq.a5.high-end", "When driving on {0}, you can choose to rent high-end models that provide a luxurious and comfortable riding experience, allowing you to feel luxury and quality."),
    FaqQ6("faq.q6", "Can the rental price of {0} be changed at any time?"),
    FaqA6("faq.a6", "Our pricing policy is constantly changing, striving to provide customers with the most favorable prices. Due to the impact of vehicle inventory, prices fluctuate daily, and even within a day. We provide customers with flexible and autonomous pricing policies. We cannot guarantee that you will definitely obtain a certain quotation. If the customer selects a quote and places an order, the quote will be confirmed."),
    FaqQ7("faq.q7", "Can I purchase child seats or other additional equipment when renting a car from {0}?"),
    FaqA7("faq.a7", "In most cases, you will make the payment at the rental counter.\n" + "In some cases, you may need to make payment when booking the vehicle.\n" + "In some cases, the items will be included in the rental fee.\n" + "When booking a vehicle, we will clearly display the relevant information."),
    FaqQ8("faq.q8", "Do I need to purchase insurance when renting a car from {0}?"),
    FaqA8("faq.a8", "Car rental insurance is an important aspect to consider when renting a car. It provides protection and peace of mind during your lease period by mitigating potential risks and liabilities. Whether you are on a road trip, business trip, or just need a temporary vehicle, having appropriate insurance can help you avoid financial troubles and unexpected expenses. Car rental companies typically offer different types of insurance, including personal injury insurance, personal property insurance, and collision damage exemption. You can carefully check whether the car rental company provides insurance and the type of insurance provided, consider the risks, and choose the appropriate car rental insurance coverage to ensure a worry free car rental experience."),
    FaqQ9("faq.q9", "How to cancel or modify a car rental order?"),
    FaqA9("faq.a9", "Sorry, you are unable to modify any rental bookings. Once the reservation is confirmed, it cannot be changed online.\n" + "You can try canceling and rebooking the pickup and return time you want, or you can try contacting a car rental store for advice."),
    FaqQ10("faq.q10", "Can I choose a car with no mileage limit when renting a car from {0}?"),
    FaqA10("faq.a10", "Yes, many car rental companies offer the option of unlimited mileage, so you can drive freely in and around the city without worrying about mileage restrictions."),
    FaqQ11("faq.q11", "How to find the best car rental deals for {0}?"),
    FaqA11("faq.a11", "If you want to explore {0} freely, it is recommended to choose the options of unlimited mileage or full fuel for driving and full fuel for returning instead of the options of half full for driving and full fuel for returning. We will also clearly display the car and van rental options that can be cancelled for free.\n" +
            "A simple car rental search on {1} can scan hundreds of suppliers' prices in seconds. Then on the search results page, you can use various filtering criteria to compare the options of your preferred car rental type and easily select the best car rental deal from all available offers."),
    FaqQ12("faq.q12", "How to rent a car at a cheaper price on {0}?"),
    FaqA12("faq.a12", "A simple car rental search on {0} can scan hundreds of suppliers' prices in seconds. Then on the search results page, you can use various filtering criteria to compare the options of your preferred car rental type and easily select the best car rental deal from all available offers.\n" + "In the past few months, {1} has had the cheapest rental prices on our website. The average daily rental price for small cars under this brand is {2}."),
    ;

    private String key;

    private String defaultValue;

    SeoShark(String key, String defaultValue) {
        this.key = key;
        this.defaultValue = defaultValue;
    }

    private String getKey() {
        return "key.seo." + key;
    }

    public String getValue(String locale) {
        if (StringUtils.isEmpty(locale)) {
            locale = "en-US";
        }
        String value = Shark.getByLocale(getKey(), locale);
        if (StringUtils.isEmpty(value)) {
            return defaultValue;
        }
        if (value.contains("$")) {
            value = value.replace("$", "");
        }
        return value;
    }

    public String getFaq(int num, String locale) {
        if (StringUtils.isEmpty(locale)) {
            locale = "en-US";
        }
        String key = getKey() + num;
        if (num == 5 && key.contains(".a")) {
            key = key + ".small";
        }
        String value = Shark.getByLocale(key, locale);
        if (StringUtils.isNotEmpty(value) && value.contains("$")) {
            value = value.replace("$", "");
        }
        return value;
    }

    public String getVendorFaq(String num, String locale) {
        if (StringUtils.isEmpty(locale)) {
            locale = "en-US";
        }
        String key = getKey() + num;
        String value = Shark.getByLocale(key, locale);
        if (StringUtils.isNotEmpty(value) && value.contains("$")) {
            value = value.replace("$", "");
        }
        return value;
    }

    public String getProvinceFaq(String num, String locale) {
        if (StringUtils.isEmpty(locale)) {
            locale = "en-US";
        }
        String key = getKey() + num;
        String value = Shark.getByLocale(key, locale);
        if (StringUtils.isNotEmpty(value) && value.contains("$")) {
            value = value.replace("$", "");
        }
        return value;
    }
}
