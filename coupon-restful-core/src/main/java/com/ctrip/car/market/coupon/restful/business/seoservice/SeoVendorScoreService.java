package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVendorScoreRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVendorScoreResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoVendorScoreService {

    @Resource
    private List<ISeoService<QuerySeoVendorScoreRequestType, QuerySeoVendorScoreResponseType>> seoServiceList;

    public QuerySeoVendorScoreResponseType queryVendorScore(QuerySeoVendorScoreRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), null);
        if (seoPage == null) {
            return new QuerySeoVendorScoreResponseType();
        }
        ISeoService<QuerySeoVendorScoreRequestType, QuerySeoVendorScoreResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoVendorScoreResponseType();
        }
        return service.query(request, seoPage);
    }
}
