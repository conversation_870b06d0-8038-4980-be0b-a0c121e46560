package com.ctrip.car.market.coupon.restful.business.seoservice.vehicle;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.contract.seo.CityVehicleInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListResponseType;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.osd.shopping.api.entity.LocationRequestInfo;
import com.ctrip.car.osd.shopping.api.entity.QueryParamDTO;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsRequestType;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SeoProvinceVehicleBusiness extends SeoBaseService<QuerySeoVehicleListRequestType, QuerySeoVehicleListResponseType> {

    public SeoProvinceVehicleBusiness() {
        super(Lists.newArrayList(SeoPage.PROVINCE, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Resource
    private SeoService seoService;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    @Resource
    private SeoCityVehicleBusiness seoCityVehicleBusiness;

    @Override
    public QuerySeoVehicleListResponseType doBusiness(QuerySeoVehicleListRequestType request, SeoPage seoPage) {
        QuerySeoVehicleListResponseType response = new QuerySeoVehicleListResponseType();
        SeoProvinceQueryParameter queryParameter = seoService.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
        if (queryParameter == null || queryParameter.getQueryParameter() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        QueryRecomdProductsResponseType soaRes = queryVehicle(request.getBaseRequest(), request.getHead(), queryParameter, false);
        response.setVehicleList(seoCityVehicleBusiness.resConvert(soaRes, request));
        if (soaRes != null && CollectionUtils.isNotEmpty(soaRes.getRecomdProductResList())) {
            response.setUrl(seoCityVehicleBusiness.buildUrl(soaRes.getSourceCountryId(), soaRes.getRecomdProductResList().get(0), null, request.getBaseRequest()));
        }
        CityVehicleInfo cityVehicleInfo = new CityVehicleInfo();
        cityVehicleInfo.setCityId(queryParameter.getQueryParameter().getCityId());
        cityVehicleInfo.setUrl(response.getUrl());
        cityVehicleInfo.setVehicleList(response.getVehicleList());
        response.setCityVehicleList(Lists.newArrayList(cityVehicleInfo));
        response.setVendorList(seoService.getTopVendor(soaRes));
        return response;
    }

    public QueryRecomdProductsResponseType queryVehicle(com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest, MobileRequestHead head, SeoProvinceQueryParameter queryParameter, boolean cache) {
        QueryRecomdProductsRequestType requestType = reqConvert(baseRequest, head, queryParameter);
        return osdShoppingProxy.queryRecomdProducts(requestType, cache);
    }

    private QueryRecomdProductsRequestType reqConvert(com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest, MobileRequestHead head, SeoProvinceQueryParameter queryParam) {
        QueryRecomdProductsRequestType soaReq = new QueryRecomdProductsRequestType();
        soaReq.setBaseRequest(new BaseRequest());
        soaReq.getBaseRequest().setRequestId(baseRequest.getRequestId());
        soaReq.getBaseRequest().setChannelId(baseRequest.getChannelId());
        soaReq.getBaseRequest().setLocale(baseRequest.getLocale());
        soaReq.getBaseRequest().setCurrencyCode(baseRequest.getCurrencyCode());
        soaReq.getBaseRequest().setUid(BaseUtils.getUidByCommon(head));
        soaReq.setSence(2);

        QueryParamDTO queryParamDTO = new QueryParamDTO();

        queryParamDTO.setPickupLocation(new LocationRequestInfo());
        queryParamDTO.getPickupLocation().setCityId(queryParam.getQueryParameter().getCityId());
        queryParamDTO.getPickupLocation().setLocationCode(queryParam.getQueryParameter().getPoiB().getPoiCode());
        queryParamDTO.getPickupLocation().setLocationType(queryParam.getQueryParameter().getPoiB().getPoiType());
        queryParamDTO.getPickupLocation().setDate(seoService.getPickupDate(7));

        soaReq.setQueryParams(Lists.newArrayList(queryParamDTO));
        return soaReq;
    }
}
