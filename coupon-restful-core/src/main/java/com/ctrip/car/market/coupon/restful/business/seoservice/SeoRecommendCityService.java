package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoRecommendCityService {

    @Resource
    private List<ISeoService<QuerySeoRecommendCityRequestType, QuerySeoRecommendCityResponseType>> seoServiceList;

    public QuerySeoRecommendCityResponseType queryRecommendCity(QuerySeoRecommendCityRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), null);
        if (seoPage == null) {
            return new QuerySeoRecommendCityResponseType();
        }
        ISeoService<QuerySeoRecommendCityRequestType, QuerySeoRecommendCityResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoRecommendCityResponseType();
        }
        return service.query(request, seoPage);
    }
}
