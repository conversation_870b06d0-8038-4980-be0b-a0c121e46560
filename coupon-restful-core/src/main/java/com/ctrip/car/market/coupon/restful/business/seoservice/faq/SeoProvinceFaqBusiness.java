package com.ctrip.car.market.coupon.restful.business.seoservice.faq;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoProvinceVehicleBusiness;
import com.ctrip.car.market.coupon.restful.contract.seo.FaqInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqResponseType;
import com.ctrip.car.market.coupon.restful.dto.FaqParameterDto;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class SeoProvinceFaqBusiness extends SeoBaseService<QuerySeoFaqRequestType, QuerySeoFaqResponseType> {

    @Resource
    private SeoService service;

    @Resource
    private SeoProvinceVehicleBusiness seoProvinceVehicleBusiness;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    public SeoProvinceFaqBusiness() {
        super(Lists.newArrayList(SeoPage.PROVINCE, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Override
    public QuerySeoFaqResponseType doBusiness(QuerySeoFaqRequestType request, SeoPage seoPage) {
        QuerySeoFaqResponseType response = new QuerySeoFaqResponseType();
        SeoProvinceQueryParameter queryParameter = service.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
        if (queryParameter == null || queryParameter.getQueryParameter() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        //报价
        QueryRecomdProductsResponseType vehicleResponse = seoProvinceVehicleBusiness.queryVehicle(request.getBaseRequest(), request.getHead(), queryParameter, false);
        response.setFaqList(getFaq(queryParameter, vehicleResponse, request));
        response.setCacheTime(osdShoppingProxy.getCacheTime(request.getBaseRequest(), queryParameter.getQueryParameter().getPoiB().getPoiCode(), null));
        response.setCarCard(osdShoppingProxy.getCarCard(request.getBaseRequest(), queryParameter.getQueryParameter().getPoiB().getPoiCode(), null));
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private List<FaqInfo> getFaq(SeoProvinceQueryParameter queryParameter, QueryRecomdProductsResponseType vehicleResponse, QuerySeoFaqRequestType request) {
        //站点
        String locale = request.getBaseRequest().getLocale();
        //币种
        String currencyCode = request.getBaseRequest().getCurrencyCode();
        //车型
        List<RecomdProductRes> productList = vehicleResponse != null ? vehicleResponse.getRecomdProductResList() : null;
        FaqParameterDto faqParameterDto = new FaqParameterDto();
        faqParameterDto.setPoiAName(service.getPoiAName(queryParameter, locale));
        faqParameterDto.setPoiBName(service.getPoiBName(queryParameter, locale));
        faqParameterDto.setCityXName(service.getCityName(queryParameter.getQueryParameter().getCityId(), locale));
        faqParameterDto.setSmallVehicle(service.getLowPriceVehicle(productList, service.getVehicleGroupSet("small")));
        faqParameterDto.setSuvVehicle(service.getLowPriceVehicle(productList, service.getVehicleGroupSet("suv")));
        faqParameterDto.setPremiumVehicle(service.getLowPriceVehicle(productList, service.getVehicleGroupSet("premium")));
        faqParameterDto.setLowPriceVehicle(service.getLowPriceVehicle(productList));
        faqParameterDto.setLowPrice(service.getLowPrice(productList, locale, currencyCode));
        faqParameterDto.setTop1Poi(service.queryCityTop1PoiName(queryParameter.getQueryParameter().getCityId(), locale));
        List<FaqInfo> result = Lists.newArrayList();
        switch (queryParameter.getSeoPage()) {
            case PROVINCE:
                result.addAll(provinceFaq(faqParameterDto, locale));
                break;
            case STATION:
                result.addAll(stationFaq(faqParameterDto, locale));
                break;
            case SCENERY:
                result.addAll(sceneryFaq(faqParameterDto, locale));
                break;
        }
        return result;
    }

    private List<FaqInfo> provinceFaq(FaqParameterDto faqParameterDto, String locale) {
        List<FaqInfo> result = Lists.newArrayList();
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("1", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("1", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getLowPrice()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("2", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("2", locale), 1, faqParameterDto.getPoiAName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("3", locale), 1, faqParameterDto.getPoiAName()),
                        provinceVehicleGroupFaq(faqParameterDto, locale))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("4", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("4", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getLowPriceVehicle()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("5", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("5", locale), 0, ""))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("6", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("6", locale), 0, ""))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("7", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("7", locale), 2, faqParameterDto.getPoiBName(), faqParameterDto.getTop1Poi()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("8", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("8", locale), 2, faqParameterDto.getPoiBName(), faqParameterDto.getLowPrice()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("9", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("9", locale), 1, faqParameterDto.getPoiBName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.ProvinceFaqQ.getProvinceFaq("10", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.ProvinceFaqA.getProvinceFaq("10", locale), 0, ""))
        );
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<FaqInfo> stationFaq(FaqParameterDto faqParameterDto, String locale) {
        List<FaqInfo> result = Lists.newArrayList();
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("1", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("1", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getLowPrice()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("2", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("2", locale), 1, faqParameterDto.getPoiAName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("3", locale), 1, faqParameterDto.getPoiAName()),
                        stationVehicleGroupFaq(faqParameterDto, locale))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("4", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("4", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getLowPriceVehicle()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("5", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("5", locale), 0, ""))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("6", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("6", locale), 0, ""))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("7", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getCityXName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("7", locale), 1, faqParameterDto.getTop1Poi()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("8", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("8", locale), 2, faqParameterDto.getPoiBName(), faqParameterDto.getLowPrice()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("9", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("9", locale), 1, faqParameterDto.getPoiBName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.StationFaqQ.getProvinceFaq("10", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.StationFaqA.getProvinceFaq("10", locale), 1, faqParameterDto.getPoiAName()))
        );
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<FaqInfo> sceneryFaq(FaqParameterDto faqParameterDto, String locale) {
        List<FaqInfo> result = Lists.newArrayList();
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("1", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("1", locale), 3, faqParameterDto.getPoiAName(), faqParameterDto.getPoiBName(), faqParameterDto.getLowPrice()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("2", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("2", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getPoiBName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("3", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("3", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getPoiBName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("4", locale), 1, faqParameterDto.getPoiAName()),
                        sceneryVehicleGroupFaq(faqParameterDto, locale))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("5", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("5", locale), 3, faqParameterDto.getPoiAName(), faqParameterDto.getLowPriceVehicle(), faqParameterDto.getPoiBName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("6", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("6", locale), 0, ""))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("7", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("7", locale), 0, ""))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("8", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("8", locale), 2, faqParameterDto.getPoiBName(), faqParameterDto.getLowPrice()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("9", locale), 1, faqParameterDto.getPoiAName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("9", locale), 1, faqParameterDto.getPoiAName()))
        );
        result.add(
                buildFaq(buildFaqContent(SeoShark.SceneryFaqQ.getProvinceFaq("10", locale), 2, faqParameterDto.getPoiAName(), faqParameterDto.getPoiBName()),
                        buildFaqContent(SeoShark.SceneryFaqA.getProvinceFaq("10", locale), 0, ""))
        );
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String provinceVehicleGroupFaq(FaqParameterDto faqParameterDto, String locale) {
        StringBuilder a = new StringBuilder();
        if (StringUtils.isNotEmpty(faqParameterDto.getSmallVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.ProvinceFaqA.getProvinceFaq("3.1", locale), faqParameterDto.getSmallVehicle()));
        }
        if (StringUtils.isNotEmpty(faqParameterDto.getSuvVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.ProvinceFaqA.getProvinceFaq("3.2", locale), faqParameterDto.getSuvVehicle()));
        }
        if (StringUtils.isNotEmpty(faqParameterDto.getPremiumVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.ProvinceFaqA.getProvinceFaq("3.3", locale), faqParameterDto.getPremiumVehicle()));
        }
        return a.toString();
    }

    private String stationVehicleGroupFaq(FaqParameterDto faqParameterDto, String locale) {
        StringBuilder a = new StringBuilder();
        if (StringUtils.isNotEmpty(faqParameterDto.getSmallVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.StationFaqA.getProvinceFaq("3.1", locale), faqParameterDto.getSmallVehicle()));
        }
        if (StringUtils.isNotEmpty(faqParameterDto.getSuvVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.StationFaqA.getProvinceFaq("3.2", locale), faqParameterDto.getSuvVehicle()));
        }
        if (StringUtils.isNotEmpty(faqParameterDto.getPremiumVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.StationFaqA.getProvinceFaq("3.3", locale), faqParameterDto.getPremiumVehicle()));
        }
        return a.toString();
    }

    private String sceneryVehicleGroupFaq(FaqParameterDto faqParameterDto, String locale) {
        StringBuilder a = new StringBuilder();
        if (StringUtils.isNotEmpty(faqParameterDto.getSmallVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.SceneryFaqA.getProvinceFaq("4.1", locale), faqParameterDto.getSmallVehicle()));
        }
        if (StringUtils.isNotEmpty(faqParameterDto.getSuvVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.SceneryFaqA.getProvinceFaq("4.2", locale), faqParameterDto.getSuvVehicle()));
        }
        if (StringUtils.isNotEmpty(faqParameterDto.getPremiumVehicle())) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.SceneryFaqA.getProvinceFaq("4.3", locale), faqParameterDto.getPremiumVehicle()));
        }
        return a.toString();
    }

    private String buildFaqContent(String contentFormat, int parameterLength, Object... parameters) {
        if (parameterLength == 0) {
            return contentFormat;
        }
        if (StringUtils.isEmpty(contentFormat) || parameters == null || parameterLength != Lists.newArrayList(parameters).stream().filter(Objects::nonNull).count()) {
            return null;
        }
        return LanguageUtils.sharkValFormat(contentFormat, parameters);
    }

    private FaqInfo buildFaq(String q, String a) {
        if (StringUtils.isEmpty(q) || StringUtils.isEmpty(a)) {
            return null;
        }
        FaqInfo result = new FaqInfo();
        result.setQuestion(q);
        result.setAnswer(a);
        return result;
    }
}
