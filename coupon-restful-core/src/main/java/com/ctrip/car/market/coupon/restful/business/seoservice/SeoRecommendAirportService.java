package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendAirportRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendAirportResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoRecommendAirportService {

    @Resource
    private List<ISeoService<QuerySeoRecommendAirportRequestType, QuerySeoRecommendAirportResponseType>> seoServiceList;

    public QuerySeoRecommendAirportResponseType queryRecommendAirport(QuerySeoRecommendAirportRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), null);
        if (seoPage == null) {
            return new QuerySeoRecommendAirportResponseType();
        }
        ISeoService<QuerySeoRecommendAirportRequestType, QuerySeoRecommendAirportResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoRecommendAirportResponseType();
        }
        return service.query(request, seoPage);
    }
}
