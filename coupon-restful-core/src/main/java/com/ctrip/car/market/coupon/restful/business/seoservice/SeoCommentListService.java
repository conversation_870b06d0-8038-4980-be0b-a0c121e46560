package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoCommentListService {

    @Resource
    private List<ISeoService<QuerySeoCommentListRequestType, QuerySeoCommentListResponseType>> seoServiceList;

    public QuerySeoCommentListResponseType queryCommentList(QuerySeoCommentListRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), request.getVendorCode());
        if (seoPage == null) {
            return new QuerySeoCommentListResponseType();
        }
        ISeoService<QuerySeoCommentListRequestType, QuerySeoCommentListResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoCommentListResponseType();
        }
        return service.query(request, seoPage);
    }
}
