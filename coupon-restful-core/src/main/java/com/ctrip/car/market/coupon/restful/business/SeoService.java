package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.commodity.common.service.types.SimpleStandardProductNameDTO;
import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorListToCacheResponseType;
import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.coupon.restful.cache.*;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.VendorCommentItemInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.VendorVehicleDto;
import com.ctrip.car.market.coupon.restful.dto.*;
import com.ctrip.car.market.coupon.restful.enums.PoiConfigType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiA;
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiB;
import com.ctrip.car.market.coupon.restful.pojo.SeoPoiConfigItem;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.carcommodity.CarCommodityCommonServiceProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.BasicDataProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.TmsProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi.GeoProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi.GlobalPoiJavaProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.vendor.VendorProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.CarMarketReadCache;
import com.ctrip.car.market.coupon.restful.utils.CurrencyUtil;
import com.ctrip.car.market.job.common.entity.seo.*;
import com.ctrip.car.osd.basicdataservice.dto.Airport;
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsResponseType;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.repository.ProvinceRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.dcs.geo.domain.value.Province;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.gs.globalpoi.soa.contract.GlobalInfo;
import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.TagAggregationInfoType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SeoService {

    private final ILog logger = LogManager.getLogger(SeoService.class);

    @Resource
    private SeoCountryCache seoCountryCache;

    @Resource
    private SeoCityCache seoCityCache;

    @Resource
    private SeoInformationCache seoInformationCache;

    @Resource
    private SeoPoiCache seoPoiCache;

    @Resource
    private CountryRepository countryRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private ProvinceRepository provinceRepository;

    @Resource
    private BasicDataProxy basicDataProxy;

    @Resource
    private TmsProxy tmsProxy;

    @Resource
    private VendorProxy vendorProxy;

    @Resource
    private TripConfig tripConfig;

    @Resource
    private SeoPlatformProxy seoPlatformProxy;

    @Resource
    private GeoProxy geoProxy;

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private SeoProvinceCache seoProvinceCache;

    @Resource
    private CarCommodityCommonServiceProxy carCommodityCommonServiceProxy;

    @Resource
    private GlobalPoiJavaProxy globalPoiJavaProxy;

    private final static Set<Integer> hmtSet = Sets.newHashSet(53, 32, 33);

    public boolean isHmt(Integer provinceId) {
        return hmtSet.contains(Optional.ofNullable(provinceId).orElse(0));
    }

    public Calendar getPickupDate(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day);
        calendar.set(Calendar.HOUR_OF_DAY, 10);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    public String getSiteUrl(String url, String locale) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        String site = "us";
        if (StringUtils.isNotEmpty(locale)) {
            if (locale.equalsIgnoreCase("en-XX")) {
                site = "www";
            } else if (locale.contains("-")) {
                site = locale.substring(locale.indexOf("-") + 1).toLowerCase();
            }
        }
        site = StringUtils.isEmpty(site) ? "us" : site;
        return url.replaceAll("www", site);
    }

    public String currencyString(BigDecimal price, String locale, String currencyCode) {
        try {
            if (StringUtils.isEmpty(locale)) {
                locale = "en-US";
            }
            return CurrencyUtil.getPriceWithCurrency(price, locale, currencyCode);
        } catch (Exception e) {
            logger.warn("currencyString", price.toString() + ":" + locale + ":" + currencyCode);
            return null;
        }
    }

    public String getCountryName(Integer countryId, String locale) {
        try {
            String countryName = seoPlatformProxy.querySeoName(1, locale, countryId.toString());
            if (StringUtils.isNotEmpty(countryName)) {
                return countryName;
            }
            Country country = countryRepository.findOne(countryId.longValue(), locale);
            if (country != null) {
                return country.getTranslationName();
            }
            logger.warn("getCountryName_error", countryId + "_" + locale);
            return "";
        } catch (Exception e) {
            logger.warn("getCountryName_error", e.toString());
            return "";
        }
    }

    public String getProvinceName(Integer provinceId, String locale) {
        String provinceName = seoPlatformProxy.querySeoName(2, locale, provinceId.toString());
        if (StringUtils.isNotEmpty(provinceName)) {
            return provinceName;
        }
        Province province = provinceRepository.findOne(provinceId.longValue(), locale);
        if (province != null) {
            return province.getTranslationName();
        }
        logger.warn("getProvince_error", provinceId + "_" + locale);
        return "";
    }

    public String getCityName(Integer cityId, String locale) {
        String cityName = seoPlatformProxy.querySeoName(3, locale, cityId.toString());
        if (StringUtils.isNotEmpty(cityName)) {
            return cityName;
        }
        City city = cityRepository.findOne(cityId.longValue(), locale);
        if (city != null) {
            return city.getTranslationName();
        }
        logger.warn("getCityName_error", cityId + "_" + locale);
        return "";
    }

    public City getCity(Integer cityId) {
        return cityRepository.findOne(cityId.longValue());
    }

    public City getCity(Integer cityId, String locale) {
        return cityRepository.findOne(cityId.longValue(), locale);
    }

    public Map<Long, City> getCity(List<Long> cityIds, String locale) {
        try {
            Map<Long, City> result = cityRepository.findMany(cityIds, locale);
            if (result == null || result.isEmpty()) {
                logger.warn("getCityName_error", StringUtils.join(cityIds, ",") + "_" + locale);
                return Maps.newHashMap();
            }
            return result;
        } catch (Exception e) {
            logger.warn("getCity", e.toString());
            return Maps.newHashMap();
        }
    }

    public String getAirportName(String airportCode, String locale) {
        String airportName = seoPlatformProxy.querySeoName(4, locale, airportCode);
        if (StringUtils.isNotEmpty(airportName)) {
            return airportName;
        }
        GetAirportsResponseType responseType = basicDataProxy.getAirport(airportCode, locale);
        if (responseType != null && CollectionUtils.isNotEmpty(responseType.getAirports())) {
            return responseType.getAirports().get(0).getAirportName();
        }
        logger.warn("getAirportName_error", airportCode + "_" + locale);
        return "";
    }

    public SeoPoi getSeoPoi(Integer poiType, String poiCode, String locale) {
        if (Objects.equals(poiType, 1)) {
            GetAirportsResponseType responseType = basicDataProxy.getAirport(poiCode, locale);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getAirports())) {
                Airport airport = responseType.getAirports().get(0);
                SeoPoi poi = new SeoPoi();
                poi.setPoiName(airport.getAirportName());
                poi.setPoiType(poiType);
                poi.setPoiCode(poiCode);
                poi.setLat(airport.getLatitude());
                poi.setLon(airport.getLongitude());
                return poi;
            }
            return null;
        }
        PlaceDetailsDTO placeDetailsDTO = geoProxy.getPoiDetail(poiCode, locale);
        if (placeDetailsDTO != null) {
            SeoPoi poi = new SeoPoi();
            poi.setPoiName(placeDetailsDTO.getName());
            poi.setPoiType(poiType);
            poi.setPoiCode(poiCode);
            poi.setLat(Optional.ofNullable(placeDetailsDTO.getLatitude()).orElse(BigDecimal.ZERO).doubleValue());
            poi.setLon(Optional.ofNullable(placeDetailsDTO.getLongitude()).orElse(BigDecimal.ZERO).doubleValue());
            return poi;
        }
        return null;
    }

    @NCache(holdMinute = 600, reHoldMinute = 30)
    public String getVendorName(String vendorCode) {
        QueryVendorListToCacheResponseType response = vendorProxy.queryVendor(vendorCode);
        return Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getList()) ? response.getList().get(0).getVendorName() : null;
    }

    public String getVehicleGroupName(String vehicleGroupId, String vehicleGroupName, String locale) {
        String key = tripConfig.getVehicleGroupKey() + vehicleGroupId;
        String value = tmsProxy.getTranslateValue(key, locale);
        return StringUtils.isEmpty(value) ? vehicleGroupName : value;
    }

    public List<SeoHotDestinatioinfoDO> queryHotDestination(Integer countryId, Integer cityId, Integer poiType, String poiCode) {
        List<SeoHotDestinatioinfoDO> list = Lists.newArrayList();
        //poi
        if (poiType != null && StringUtils.isNotEmpty(poiCode)) {
            list = queryDestinationByPoi(poiType, poiCode);
        } else if (Optional.ofNullable(cityId).orElse(0) > 0) {
            //city
            list = queryDestinationByCity(cityId);
        } else if (Optional.ofNullable(countryId).orElse(0) > 0) {
            //country
            list = queryDestinationByCountry(countryId);
        }
        //只返回机场
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(l -> Objects.equals(l.getPoiType(), 1)).toList();
        }
        return null;
    }

    public SeoHotDestinatioinfoDO queryHotDestinationFirst(Integer countryId, Integer cityId, Integer poiType, String poiCode) {
        List<SeoHotDestinatioinfoDO> list = queryHotDestination(countryId, cityId, poiType, poiCode);
        if (CollectionUtils.isEmpty(list)) {
            //读取自定义配置
            return queryCityDefaultAirport(poiCode, cityId);
        }
        return list.stream().filter(l -> l.getOrderNum() != null && StringUtils.isNotEmpty(l.getPoiCode()))
                .max(Comparator.comparing(SeoHotDestinatioinfoDO::getOrderNum)).orElse(null);
    }

    public SeoHotCountryinfoDO queryHotCountryByCountry(Integer countryId) {
        try {
            return seoCountryCache.queryByCountry(countryId);
        } catch (Exception e) {
            logger.error("queryHotCountryByCountry", e);
            return null;
        }
    }


    public List<SeoHotCityinfoDO> queryHotCity(Integer countryId) {
        try {
            return seoCityCache.queryByCountry(countryId);
        } catch (Exception e) {
            logger.error("queryHotCity", e);
            return null;
        }
    }


    public SeoHotCityinfoDO queryHotCityByCity(Integer cityId) {
        try {
            return seoCityCache.queryByCity(cityId);
        } catch (Exception e) {
            logger.error("queryHotCityByCity", e);
            return null;
        }
    }


    private List<SeoHotDestinatioinfoDO> queryDestinationByCountry(Integer countryId) {
        try {
            return seoPoiCache.queryByCountry(countryId);
        } catch (Exception e) {
            logger.error("queryDestinationByCountry", e);
            return null;
        }
    }


    private List<SeoHotDestinatioinfoDO> queryDestinationByCity(Integer cityId) {
        try {
            return seoPoiCache.queryByCity(cityId);
        } catch (Exception e) {
            logger.error("queryDestinationByCity", e);
            return null;
        }
    }

    private List<SeoHotDestinatioinfoDO> queryDestinationByPoi(Integer poiType, String poiCode) {
        try {
            return seoPoiCache.queryByPoiCode(poiType, poiCode);
        } catch (Exception e) {
            logger.error("queryDestinationByPoi", e);
            return null;
        }
    }

    public SeoHotInformationDO queryInformationByPoi(Integer poiType, String poiCode) {
        try {
            return seoInformationCache.queryInformation(poiType, poiCode);
        } catch (Exception e) {
            logger.error("queryInformationByPoi", e);
            return null;
        }
    }

    public Map<Integer, Long> getCityOrderNum(List<SeoHotDestinatioinfoDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<SeoHotDestinatioinfoDO>> cityMap = list.stream().collect(Collectors.groupingBy(SeoHotDestinatioinfoDO::getCityId));
        Map<Integer, Long> result = Maps.newHashMap();
        for (Map.Entry<Integer, List<SeoHotDestinatioinfoDO>> kv : cityMap.entrySet()) {
            result.put(kv.getKey(), kv.getValue().stream().mapToLong(SeoHotDestinatioinfoDO::getOrderNum).sum() * -1);
        }
        return result;
    }

    public String commentDateFormat(Long time, String locale) {
        try {
            Date date = new Date(time);
            L10n.DateTimeFormatter format = L10n.dateTimeFormatter(locale);
            String dateStr = format.ymdShortString(date);
            return dateStr;
        } catch (Exception e) {
            return time.toString();
        }
    }

    public String queryPoiName(Integer poiType, String poiCode, String locale) {
        if (Objects.equals(poiType, 1)) {
            return getAirportName(poiCode, locale);
        }
        if (NumberUtils.isNumber(poiCode)) {
            PlaceDetailsDTO placeDetailsDTO = geoProxy.getPoiDetail(poiCode, locale);
            return placeDetailsDTO == null ? "" : placeDetailsDTO.getName();
        }
        return "";
    }

    public PlaceDetailsDTO queryCityDefaultPoi(String poiCode, Integer cityId, String locale) {
        if (StringUtils.isNotEmpty(poiCode) || Optional.ofNullable(cityId).orElse(0) <= 0) {
            return null;
        }
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        if (item == null || StringUtils.isEmpty(item.getPoiCode()) || !NumberUtils.isNumber(item.getPoiCode())) {
            return null;
        }
        PlaceDetailsDTO result = geoProxy.getPoiDetail(item.getPoiCode(), locale);
        if (result != null) {
            result.setType(item.getPoiType().toString());
        }
        return result;
    }

    public SeoHotDestinatioinfoDO queryCityDefaultAirport(String poiCode, Integer cityId) {
        if (StringUtils.isNotEmpty(poiCode) || Optional.ofNullable(cityId).orElse(0) <= 0) {
            return null;
        }
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getPoiType(), 1) && Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        if (item == null || StringUtils.isEmpty(item.getPoiCode())) {
            return null;
        }
        SeoHotDestinatioinfoDO airport = new SeoHotDestinatioinfoDO();
        airport.setPoiType(item.getPoiType());
        airport.setPoiCode(item.getPoiCode());
        airport.setCityId(cityId);
        airport.setOrderNum(0);
        City city = cityRepository.findOne(cityId.longValue());
        airport.setCountryId(city != null ? city.getCountryId().intValue() : 0);
        return airport;
    }

    public SeoHotDestinatioinfoDO queryCityDefaultPoi(String poiCode, Integer cityId) {
        if (StringUtils.isNotEmpty(poiCode) || Optional.ofNullable(cityId).orElse(0) <= 0) {
            return null;
        }
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        if (item == null || StringUtils.isEmpty(item.getPoiCode())) {
            return null;
        }
        SeoHotDestinatioinfoDO poi = new SeoHotDestinatioinfoDO();
        poi.setPoiType(item.getPoiType());
        poi.setPoiCode(item.getPoiCode());
        poi.setCityId(cityId);
        poi.setOrderNum(0);
        return poi;
    }

    public Integer queryPoiType(String poiCode) {
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> StringUtils.equalsIgnoreCase(poiCode, l.getPoiCode())).findFirst().orElse(null);
        return item == null ? 0 : item.getPoiType();
    }

    /**
     * 查询供应商热门城市，使用默认兜底逻辑，只用于获取poi
     */
    public SeoHotVendorInformationDO queryVendorHotCityDefault(String vendorCode) {
        SeoHotVendorInformationDO result = queryVendorHotCity(vendorCode);
        //使用qconfig兜底，qconfig没配置使用LAX
        if (result != null) {
            return result;
        }
        VendorCityDto configDto = tripConfig.getVendorCityList().stream().filter(l -> StringUtils.equalsIgnoreCase(l.getVendorCode(), vendorCode)).findFirst().orElse(null);
        if (configDto != null && CollectionUtils.isNotEmpty(configDto.getCityIdList()) && StringUtils.isNotEmpty(configDto.getCityIdList().getFirst().getPoiCode())) {
            VendorCityItem item = configDto.getCityIdList().getFirst();
            SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
            value.setCityId(item.getCityId());
            value.setPoiType(item.getPoiType());
            value.setPoiCode(item.getPoiCode());
            value.setVendorCode(vendorCode);
            return value;
        }
        SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
        value.setCityId(347);
        value.setPoiType(1);
        value.setPoiCode("LAX");
        value.setVendorCode(vendorCode);
        return value;
    }

    /**
     * 查询供应商热门城市，使用默认兜底逻辑，只用于获取poi
     */
    public SeoHotVendorInformationDO queryVendorHotCityDefault(String vendorCode, Integer cityId) {
        SeoHotVendorInformationDO result = queryVendorCity(vendorCode, cityId);
        //使用qconfig兜底，qconfig没配置使用LAX
        if (result != null) {
            return result;
        }
        VendorCityDto configDto = tripConfig.getVendorCityList().stream().filter(l -> StringUtils.equalsIgnoreCase(l.getVendorCode(), vendorCode)).findFirst().orElse(null);
        VendorCityItem item = configDto == null || CollectionUtils.isEmpty(configDto.getCityIdList()) ? null
                : configDto.getCityIdList().stream().filter(l -> Objects.equals(l.getCityId(), cityId) && StringUtils.isNotEmpty(l.getPoiCode())).findFirst().orElse(null);
        if (item != null) {
            SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
            value.setCityId(item.getCityId());
            value.setPoiType(item.getPoiType());
            value.setPoiCode(item.getPoiCode());
            value.setVendorCode(vendorCode);
            return value;
        }
        SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
        value.setCityId(347);
        value.setPoiType(1);
        value.setPoiCode("LAX");
        value.setVendorCode(vendorCode);
        return value;
    }

    /**
     * 查询供应商热门城市，搜索量第一的城市
     */
    public SeoHotVendorInformationDO queryVendorHotCity(String vendorCode) {
        List<SeoHotVendorInformationDO> vendorCityList = seoVendorCache.queryVendorInformation(vendorCode);
        if (CollectionUtils.isEmpty(vendorCityList)) {
            return null;
        }
        Map<String, List<SeoHotVendorInformationDO>> vendorCityMap = vendorCityList.stream().collect(Collectors.groupingBy(l -> l.getVendorCode() + "_" + l.getCityId()));
        List<SeoHotVendorInformationDO> result = Lists.newArrayList();
        for (Map.Entry<String, List<SeoHotVendorInformationDO>> kv : vendorCityMap.entrySet()) {
            result.add(kv.getValue().get(0));
        }
        return result.stream().max(Comparator.comparing(SeoHotVendorInformationDO::getSearchNum)).orElse(null);
    }

    /**
     * 查询供应商top3热门城市，按搜索量排序
     */
    public List<SeoHotVendorInformationDO> queryVendorTop3City(String vendorCode) {
        List<SeoHotVendorInformationDO> vendorCityList = seoVendorCache.queryVendorInformation(vendorCode);
        if (CollectionUtils.isEmpty(vendorCityList)) {
            return Lists.newArrayList();
        }
        Map<String, List<SeoHotVendorInformationDO>> vendorCityMap = vendorCityList.stream().collect(Collectors.groupingBy(l -> l.getVendorCode() + "_" + l.getCityId()));
        List<SeoHotVendorInformationDO> result = Lists.newArrayList();
        for (Map.Entry<String, List<SeoHotVendorInformationDO>> kv : vendorCityMap.entrySet()) {
            result.add(kv.getValue().get(0));
        }
        return result.stream().sorted(Comparator.comparing(SeoHotVendorInformationDO::getSearchNum).reversed()).limit(3).collect(Collectors.toList());
    }

    /**
     * 查询供应商城市
     */
    public SeoHotVendorInformationDO queryVendorCity(String vendorCode, Integer cityId) {
        List<SeoHotVendorInformationDO> vendorCityList = seoVendorCache.queryVendorInformation(vendorCode);
        if (CollectionUtils.isEmpty(vendorCityList)) {
            return null;
        }
        List<SeoHotVendorInformationDO> cityList = vendorCityList.stream().filter(l -> Objects.equals(l.getCityId(), cityId)).collect(Collectors.toList());
        return cityList.stream().max(Comparator.comparing(SeoHotVendorInformationDO::getSearchNum)).orElse(null);
    }

    @NCache(holdMinute = 600)
    public String queryVehicleName(Long id, String locale) {
        SimpleStandardProductNameDTO simpleStandardProductNameDTO = carCommodityCommonServiceProxy.multiLanguageQuery(id, locale);
        if (simpleStandardProductNameDTO == null) {
            return null;
        }
        return simpleStandardProductNameDTO.getName();
    }

    public String queryUrlName(Long id, int type) {
        try {
            String urlName = "";
            if (type == 1) {
                Country country = countryRepository.findOne(id);
                urlName = country != null ? country.getEnglishName() : "";
            } else {
                City city = cityRepository.findOne(id);
                urlName = city != null ? city.getEnglishName() : "";
            }
            return urlName.toLowerCase().replace(" ", "-").replace("’", "").replace(".", "").replace("?", "");
        } catch (Exception e) {
            logger.warn("queryCountryUrlName", e);
            return "";
        }
    }

    public Integer getVendorCount(QuerySeoFaqRequestType request) {
        try {
            Integer countryId = request.getCountryId();
            //机场页面
            if (Optional.ofNullable(request.getPoiType()).orElse(0) > 0 && StringUtils.isNotEmpty(request.getPoiCode())) {
                List<SeoHotDestinatioinfoDO> airportList = queryDestinationByPoi(request.getPoiType(), request.getPoiCode());
                if (CollectionUtils.isEmpty(airportList)) {
                    return null;
                }
                countryId = airportList.getFirst().getCountryId();
            }
            //城市页面
            if (Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
                SeoHotCityinfoDO cityinfoDO = queryHotCityByCity(request.getCityId());
                if (cityinfoDO == null) {
                    return null;
                }
                countryId = cityinfoDO.getCountryId();
            }
            if (Optional.ofNullable(countryId).orElse(0) > 0) {
                List<SeoHotInformationDO> list = seoInformationCache.queryInformationByCountry(countryId);
                return CollectionUtils.isEmpty(list) ? null : list.stream().map(SeoHotInformationDO::getVendorId).distinct().toList().size();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public Integer getVendorStoreCount(String vendorCode) {
        try {
            List<SeoHotVendorInformationDO> list = seoVendorCache.queryVendorInformation(vendorCode);
            return CollectionUtils.isEmpty(list) ? null : list.stream().map(SeoHotVendorInformationDO::getStoreNum).reduce(Integer::sum).orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 活动top3供应商报价
     *
     * @param response
     * @return
     */
    public List<VendorVehicleDto> getTopVendor(QueryRecomdProductsResponseType response) {
        if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList()) || CollectionUtils.isEmpty(response.getRecomdProductResList().getFirst().getProducts())) {
            return Lists.newArrayList();
        }
        Set<String> vendorSet = Sets.newHashSet();
        //返回top3供应商
        return response.getRecomdProductResList().getFirst().getProducts().stream()
                .filter(l -> l.getVendorInfo() != null && StringUtils.isNotEmpty(l.getVendorInfo().getVendorName())
                        && StringUtils.isNotEmpty(l.getVendorInfo().getBizVendorCode()) && l.getVehicle() != null && l.getPrice() != null).map(l -> {
                    if (vendorSet.contains(l.getVendorInfo().getBizVendorCode())) {
                        return null;
                    }
                    vendorSet.add(l.getVendorInfo().getBizVendorCode());
                    VendorVehicleDto item = new VendorVehicleDto();
                    item.setVendorCode(l.getVendorInfo().getBizVendorCode());
                    item.setVendorName(l.getVendorInfo().getVendorName());
                    item.setCarType(l.getVehicle().getVehicleName());
                    item.setPrice(l.getPrice().getCurrentDailyPrice());
                    return item;
                }).filter(Objects::nonNull).limit(3).toList();
    }

    public SeoHotProvinceinfoDO queryProvince(Integer provinceId) {
        try {
            return seoProvinceCache.queryByProvince(provinceId);
        } catch (Exception e) {
            logger.error("queryProvince", e);
            return null;
        }
    }

    public String queryCityTop1PoiName(Integer cityId, String locale) {
        List<SeoHotDestinatioinfoDO> cityPoiList = queryDestinationByCity(cityId);
        SeoHotDestinatioinfoDO poi = CollectionUtils.isNotEmpty(cityPoiList) ? cityPoiList.stream().filter(l -> Objects.equals(l.getPoiType(), 1)).max(Comparator.comparing(SeoHotDestinatioinfoDO::getOrderNum)).orElse(null) : null;
        return poi != null ? getAirportName(poi.getPoiCode(), locale) : null;
    }

    /**
     * 构建seo查询参数
     */
    public SeoProvinceQueryParameter buildQueryParameter(Integer provinceId, Integer poiType, String poiCode) {
        SeoProvinceQueryParameter parameter = new SeoProvinceQueryParameter();
        if (Optional.ofNullable(provinceId).orElse(0) > 0) {
            parameter.setSeoPage(SeoPage.PROVINCE);
            parameter.setQueryParameter(buildProvincePoi(provinceId));
        } else if (Optional.ofNullable(poiType).orElse(0) == 2 && StringUtils.isNotEmpty(poiCode)) {
            parameter.setSeoPage(SeoPage.STATION);
            parameter.setQueryParameter(buildStationPoi(poiType, poiCode));
        } else if (Optional.ofNullable(poiType).orElse(0) == 20 && StringUtils.isNotEmpty(poiCode)) {
            parameter.setSeoPage(SeoPage.SCENERY);
            parameter.setQueryParameter(queryPoiByPoiType(PoiConfigType.SCENERY.getType(), poiCode));
        }
        return parameter;
    }

    private SeoPoiConfigItem buildProvincePoi(Integer provinceId) {
        SeoHotProvinceinfoDO provinceDO = seoProvinceCache.queryByProvince(provinceId);
        if (provinceDO == null) {
            return null;
        }
        SeoPoiConfigItem poiConfigItem = queryPoiByPoiType(PoiConfigType.PROVINCE.getType(), provinceId.toString());
        if (poiConfigItem != null) {
            return poiConfigItem;
        }
        //省份下所搜量第一的机场poi
        List<SeoHotCityinfoDO> provinceCityList = seoCityCache.queryByProvince(provinceId);
        if (CollectionUtils.isNotEmpty(provinceCityList)) {
            SeoHotDestinatioinfoDO provinceAirport = seoPoiCache.queryTop1Airport(provinceCityList.stream().map(SeoHotCityinfoDO::getCityId).distinct().collect(Collectors.toList()));
            if (provinceAirport != null) {
                return buildProvincePoi(provinceDO, provinceAirport);
            }
        }
        //省份所在国家下所搜量第一的机场poi
        SeoHotDestinatioinfoDO countryAirport = seoPoiCache.queryTop1Airport(provinceDO.getCountryId());
        if (countryAirport != null) {
            return buildProvincePoi(provinceDO, countryAirport);
        }
        //兜底LAX
        return buildProvincePoi(provinceDO, null);
    }

    private SeoPoiConfigItem buildProvincePoi(SeoHotProvinceinfoDO provinceDO, SeoHotDestinatioinfoDO destination) {
        SeoPoiConfigItem poiConfigItem = new SeoPoiConfigItem();
        poiConfigItem.setPoiA(new SeoPoiA());
        poiConfigItem.getPoiA().setType(PoiConfigType.PROVINCE.getType());
        poiConfigItem.getPoiA().setPoiId(provinceDO.getProvinceId().longValue());
        poiConfigItem.setPoiB(new SeoPoiB());
        if (destination != null) {
            poiConfigItem.getPoiB().setPoiType(destination.getPoiType());
            poiConfigItem.getPoiB().setPoiCode(destination.getPoiCode());
            poiConfigItem.setCityId(destination.getCityId());
        } else {
            poiConfigItem.getPoiB().setPoiType(1);
            poiConfigItem.getPoiB().setPoiCode("LAX");
            poiConfigItem.setCityId(347);
        }
        poiConfigItem.setCountryId(provinceDO.getCountryId());
        return poiConfigItem;
    }

    private SeoPoiConfigItem buildStationPoi(Integer poiType, String poiCode) {
        SeoPoiConfigItem poiConfigItem = queryPoiByPoiType(PoiConfigType.STATION.getType(), poiCode);
        if (poiConfigItem != null) {
            City city = poiConfigItem.getCityId() != null ? cityRepository.findOne(poiConfigItem.getCityId().longValue()) : null;
            poiConfigItem.setCountryId(city != null ? city.getCountryId().intValue() : 0);
            return poiConfigItem;
        }
        List<SeoHotDestinatioinfoDO> destinationList = queryDestinationByPoi(poiType, poiCode);
        if (CollectionUtils.isEmpty(destinationList)) {
            return null;
        }
        SeoPoiConfigItem defaultConfigItem = new SeoPoiConfigItem();
        defaultConfigItem.setPoiA(new SeoPoiA());
        defaultConfigItem.getPoiA().setType(PoiConfigType.STATION.getType());
        defaultConfigItem.getPoiA().setPoiId(Long.valueOf(destinationList.getFirst().getPoiCode()));
        defaultConfigItem.setPoiB(new SeoPoiB());
        defaultConfigItem.getPoiB().setPoiType(destinationList.getFirst().getPoiType());
        defaultConfigItem.getPoiB().setPoiCode(destinationList.getFirst().getPoiCode());
        defaultConfigItem.setCityId(destinationList.getFirst().getCityId());
        defaultConfigItem.setCountryId(destinationList.getFirst().getCountryId());
        return defaultConfigItem;
    }

    private SeoPoiConfigItem queryPoiByPoiType(String poiType, String poiCode) {
        SeoPoiConfigItem item = tripConfig.getSeoPoiConfigList().stream().filter(l -> Objects.equals(l.getPoiA().getType(), poiType) && Objects.equals(l.getPoiA().getPoiId().toString(), poiCode)).findFirst().orElse(null);
        if (item == null) {
            return item;
        }
        City city = cityRepository.findOne(item.getCityId().longValue());
        item.setCountryId(city != null ? city.getCountryId().intValue() : 0);
        return item;
    }

    public String getGlobalPoiName(Long poiCode, String locale) {
        GlobalInfo globalInfo = globalPoiJavaProxy.getPoi(poiCode, locale);
        if (globalInfo != null && globalInfo.getPoiName() != null) {
            return StringUtils.isNotEmpty(globalInfo.getPoiName().getGlobal()) ? globalInfo.getPoiName().getGlobal() : globalInfo.getPoiName().getEn();
        }
        return "";
    }

    public String getPoiAName(SeoProvinceQueryParameter queryParameter, String locale) {
        return switch (queryParameter.getSeoPage()) {
            case PROVINCE ->
                    getProvinceName(queryParameter.getQueryParameter().getPoiA().getPoiId().intValue(), locale);
            case STATION -> queryPoiName(2, queryParameter.getQueryParameter().getPoiA().getPoiId().toString(), locale);
            case SCENERY -> getGlobalPoiName(queryParameter.getQueryParameter().getPoiA().getPoiId(), locale);
            default -> "";
        };
    }

    public String getPoiBName(SeoProvinceQueryParameter queryParameter, String locale) {
        return switch (queryParameter.getQueryParameter().getPoiB().getPoiType()) {
            case 1 -> getAirportName(queryParameter.getQueryParameter().getPoiB().getPoiCode(), locale);
            case 2, 20 -> queryPoiName(2, queryParameter.getQueryParameter().getPoiB().getPoiCode(), locale);
            default -> "";
        };
    }

    public String getLowPrice(List<RecomdProductRes> productList, String locale, String currencyCode) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return currencyString(price, locale, currencyCode);
    }

    public BigDecimal getLowPriceValue(List<RecomdProductRes> productList) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        return productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
    }

    public String getLowPriceVehicle(List<RecomdProductRes> productList) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        RecomdProduct product = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).min(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).orElse(null);
        if (product == null) {
            return null;
        }
        return product.getVehicle().getVehicleName();
    }

    public String getLowVendor(List<RecomdProductRes> productList) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        RecomdProduct product = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).min(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).orElse(null);
        if (product == null) {
            return null;
        }
        return getVendorName(product.getVehicle().getVendorCode());
    }

    public String getVendorPrice(List<RecomdProductRes> productList, String locale, String currencyCode, String vendorCode) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        if (StringUtils.isEmpty(vendorCode)) {
            return null;
        }
        Set<String> smallCodeSet = getVehicleGroupSet("small");
        if (CollectionUtils.isEmpty(smallCodeSet)) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null
                && l.getVehicle() != null && StringUtils.isNotEmpty(l.getVehicle().getGroupCode()) && smallCodeSet.contains(l.getVehicle().getGroupCode())
                && StringUtils.equalsIgnoreCase(l.getVehicle().getVendorCode(), vendorCode)).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return currencyString(price, locale, currencyCode);
    }

    public String getVehicleGroupPrice(List<RecomdProductRes> productList, String locale, String currencyCode, Set<String> vehicleGroupSet) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        if (CollectionUtils.isEmpty(vehicleGroupSet)) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null
                && l.getVehicle() != null && StringUtils.isNotEmpty(l.getVehicle().getGroupCode()) && vehicleGroupSet.contains(l.getVehicle().getGroupCode())).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return currencyString(price, locale, currencyCode);
    }

    public String getLowPriceVehicle(List<RecomdProductRes> productList, Set<String> vehicleGroupSet) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        if (CollectionUtils.isEmpty(vehicleGroupSet)) {
            return null;
        }
        RecomdProduct product = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null
                && l.getVehicle() != null && StringUtils.isNotEmpty(l.getVehicle().getGroupCode()) && vehicleGroupSet.contains(l.getVehicle().getGroupCode())).sorted(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).findFirst().orElse(null);
        if (product == null || product.getVehicle() == null) {
            return null;
        }
        return product.getVehicle().getVehicleName();
    }

    public Set<String> getVehicleGroupSet(String vehicleGroup) {
        return tripConfig.getVehicleGroupMappingList().stream().filter(l -> StringUtils.equalsIgnoreCase(vehicleGroup, l.getValue())).map(KeyValueDto::getKey).collect(Collectors.toSet());
    }

    public String getVendorLogo(String vendorCode) {
        VendorLogoItem logoItem = tripConfig.getVendorLogoList().stream().filter(li -> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(li.getVendorCode(), vendorCode.trim())).findFirst().orElse(null);
        if (logoItem != null) {
            return logoItem.getLogo();
        }
        return CarMarketReadCache.get("car.market.seo.vendor.logo." + vendorCode.toLowerCase());
    }

    public List<VendorCommentItemInfo> dbSubItemConvert(List<SubItemConfigDto> list, String locale) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(l -> {
            VendorCommentItemInfo item = new VendorCommentItemInfo();
            item.setScore(scoreFormat(l.getScoreAvg()));
            item.setType(subItemTypeMapping(l.getSubItemConfigId()));
            item.setName(subItemNameMapping(l.getSubItemConfigId(), locale));
            return item;
        }).collect(Collectors.toList());
    }

    public List<VendorCommentItemInfo> apiSubItemConvert(List<TagAggregationInfoType> list, String locale) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(l -> {
            VendorCommentItemInfo item = new VendorCommentItemInfo();
            item.setScore(scoreFormat(l.getScoreAvg()));
            item.setType(subItemTypeMapping(l.getTagName()));
            item.setName(subItemNameMapping(l.getTagName(), locale));
            return item;
        }).collect(Collectors.toList());
    }

    private BigDecimal scoreFormat(BigDecimal score) {
        return score == null ? null : score.setScale(1, RoundingMode.UP);
    }

    private String subItemNameMapping(String code, String locale) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        return switch (code) {
            case "83" -> SeoShark.Comment_Item_Car.getValue(locale);
            case "84" -> SeoShark.Comment_Item_Clear.getValue(locale);
            case "85" -> SeoShark.Comment_Item_Pick.getValue(locale);
            case "86" -> SeoShark.Comment_Item_Service.getValue(locale);
            default -> "";
        };
    }

    private Integer subItemTypeMapping(String code) {
        if (StringUtils.isEmpty(code)) {
            return 0;
        }
        return switch (code) {
            case "83" -> 1;
            case "84" -> 2;
            case "85" -> 3;
            case "86" -> 4;
            default -> 0;
        };
    }
}
