package com.ctrip.car.market.coupon.restful.enums;

import org.apache.commons.lang.StringUtils;

import java.util.Objects;
import java.util.Optional;

public enum SeoPage {

    TRIP_HOME(0),
    PROVINCE(1),
    STATION(2),
    SCENERY(3),
    COUNTRY(4),
    CITY(5),
    AIRPORT(6),
    VENDOR(7),
    VENDOR_CITY(8);

    private int type;

    SeoPage(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static SeoPage getPage(Integer countryId, Integer provinceId, Integer cityId, Integer poiType, String poiCode, String vendorCode) {
        if (StringUtils.isNotEmpty(vendorCode) && Optional.ofNullable(cityId).orElse(0) > 0) {
            return VENDOR_CITY;
        }
        if (StringUtils.isNotEmpty(vendorCode)) {
            return VENDOR;
        }
        if (Objects.equals(poiType, 1) && StringUtils.isNotEmpty(poiCode)) {
            return AIRPORT;
        }
        if (Objects.equals(poiType, 2) && StringUtils.isNotEmpty(poiCode)) {
            return STATION;
        }
        if (Objects.equals(poiType, 20) && StringUtils.isNotEmpty(poiCode)) {
            return SCENERY;
        }
        if (Optional.ofNullable(cityId).orElse(0) > 0) {
            return CITY;
        }
        if (Optional.ofNullable(provinceId).orElse(0) > 0) {
            return PROVINCE;
        }
        if (Optional.ofNullable(countryId).orElse(0) > 0) {
            return COUNTRY;
        }
        return TRIP_HOME;
    }
}
