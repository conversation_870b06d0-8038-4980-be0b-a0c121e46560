package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoInformationService {

    @Resource
    private List<ISeoService<QuerySeoInformationRequestType, QuerySeoInformationResponseType>> seoServiceList;

    public QuerySeoInformationResponseType queryInformation(QuerySeoInformationRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), request.getVendorCode());
        if (seoPage == null) {
            return new QuerySeoInformationResponseType();
        }
        ISeoService<QuerySeoInformationRequestType, QuerySeoInformationResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoInformationResponseType();
        }
        return service.query(request, seoPage);
    }
}
