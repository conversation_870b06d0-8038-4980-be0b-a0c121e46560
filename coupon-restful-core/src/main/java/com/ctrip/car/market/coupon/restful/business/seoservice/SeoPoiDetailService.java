package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@Component
public class SeoPoiDetailService {

    @Resource
    private List<ISeoService<QuerySeoPoiDetailRequestType, QuerySeoPoiDetailResponseType>> seoServiceList;

    public QuerySeoPoiDetailResponseType queryPoiDetail(QuerySeoPoiDetailRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), request.getVendorCode());
        if (seoPage == null) {
            return new QuerySeoPoiDetailResponseType();
        }
        ISeoService<QuerySeoPoiDetailRequestType, QuerySeoPoiDetailResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoPoiDetailResponseType();
        }
        return service.query(request, seoPage);
    }
}
