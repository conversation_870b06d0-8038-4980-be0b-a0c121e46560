package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.gs.globalpoi.soa.contract.GlobalInfo;
import com.ctrip.gs.globalpoi.soa.contract.GlobalPoiJavaClient;
import com.ctrip.gs.globalpoi.soa.contract.PoiGlobalizationDetailRequestType;
import com.ctrip.gs.globalpoi.soa.contract.PoiGlobalizationDetailResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class GlobalPoiJavaProxy {

    private final ILog log = LogManager.getLogger(GlobalPoiJavaProxy.class);

    private GlobalPoiJavaClient client = GlobalPoiJavaClient.getInstance();

    public GlobalInfo getPoi(Long poiCode, String locale) {
        try {
            PoiGlobalizationDetailRequestType request = new PoiGlobalizationDetailRequestType();
            request.setLang(locale);
            request.setPoiIds(Lists.newArrayList(poiCode));
            PoiGlobalizationDetailResponseType response = client.poiGlobalizationDetail(request);
            if (CollectionUtils.isNotEmpty(response.getResult())) {
                return response.getResult().get(0);
            }
            return null;
        } catch (Exception e) {
            log.warn("getPoi", e);
            return null;
        }
    }
}
