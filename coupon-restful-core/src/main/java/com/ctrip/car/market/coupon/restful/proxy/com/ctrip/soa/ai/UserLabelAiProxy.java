package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.tour.ai.userlabelservice.TourAiUserLabelServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import soa.ctrip.com.tour.ai.user.label.BatchUserLabelInfoRequestType;
import soa.ctrip.com.tour.ai.user.label.BatchUserLabelInfoResponseType;
import soa.ctrip.com.tour.ai.user.label.LabelInfo;

import java.util.List;

/**
 * 判断是否是新客
 */
@Service
public class UserLabelAiProxy {

    private ILog logger = LogManager.getLogger(UserLabelAiProxy.class);

    private TourAiUserLabelServiceClient tourAiUserLabelServiceClient = TourAiUserLabelServiceClient.getInstance();

    public boolean judgeIsNewGuest(String uid, List<String> labelIds) {
        boolean isNewGuest = false;
        if (CollectionUtils.isEmpty(labelIds) || StringUtils.isBlank(uid)) {
            return isNewGuest;
        }
        BatchUserLabelInfoRequestType batchUserLabelInfoRequestType = new BatchUserLabelInfoRequestType();
        batchUserLabelInfoRequestType.setUid(uid);
        batchUserLabelInfoRequestType.setLabelIds(labelIds);
        try {
            BatchUserLabelInfoResponseType responseType = tourAiUserLabelServiceClient.getBatchUserLabelInfos(batchUserLabelInfoRequestType);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getLabelInfos())) {
               isNewGuest = responseType.getLabelInfos().stream().allMatch(x -> "true".equalsIgnoreCase(x.getLabelValue()));
            }
        } catch (Exception e) {
            logger.error("getBatchUserLabelInfos error", e.getMessage());
        }
        return isNewGuest;
    }

    public  List<LabelInfo> queryUserLabelCode(String uid, List<String> labelIds) {
        if (CollectionUtils.isEmpty(labelIds) || StringUtils.isBlank(uid)) {
            return null;
        }
        BatchUserLabelInfoRequestType batchUserLabelInfoRequestType = new BatchUserLabelInfoRequestType();
        batchUserLabelInfoRequestType.setUid(uid);
        batchUserLabelInfoRequestType.setLabelIds(labelIds);
        try {
            BatchUserLabelInfoResponseType responseType = tourAiUserLabelServiceClient.getBatchUserLabelInfos(batchUserLabelInfoRequestType);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getLabelInfos())) {
               return responseType.getLabelInfos();
            }
        } catch (Exception e) {
            logger.error("queryUserLabelCode error", e.getMessage());
        }
        return null;
    }
}
