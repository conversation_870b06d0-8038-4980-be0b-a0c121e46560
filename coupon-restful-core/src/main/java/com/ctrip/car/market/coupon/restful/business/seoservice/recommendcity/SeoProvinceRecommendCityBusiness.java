package com.ctrip.car.market.coupon.restful.business.seoservice.recommendcity;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.RecommendCityInfo;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.dcs.geo.domain.value.City;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class SeoProvinceRecommendCityBusiness extends SeoBaseService<QuerySeoRecommendCityRequestType, QuerySeoRecommendCityResponseType> {

    public SeoProvinceRecommendCityBusiness() {
        super(Lists.newArrayList(SeoPage.PROVINCE, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Resource
    private SeoService seoService;

    @Resource
    private TripConfig tripConfig;

    @Override
    public QuerySeoRecommendCityResponseType doBusiness(QuerySeoRecommendCityRequestType request, SeoPage seoPage) {
        QuerySeoRecommendCityResponseType response = new QuerySeoRecommendCityResponseType();
        SeoProvinceQueryParameter queryParameter = seoService.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
        if (queryParameter == null || queryParameter.getQueryParameter() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        Integer countryId = queryParameter.getQueryParameter().getCountryId();
        List<SeoHotCityinfoDO> cityList = seoService.queryHotCity(countryId);
        if (CollectionUtils.isEmpty(cityList)) {
            return response;
        }
        List<SeoHotDestinatioinfoDO> destanationList = seoService.queryHotDestination(countryId, null, null, null);
        String locale = request.getBaseRequest().getLocale();
        String countryName = seoService.getCountryName(countryId, locale);
        response.setCityList(convert(cityList, destanationList, locale));
        response.setTitle(LanguageUtils.sharkValFormat(SeoShark.RecommendCityTitle.getValue(locale), countryName));
        response.setSubTitle(LanguageUtils.sharkValFormat(SeoShark.RecommendCitySubTitle.getValue(locale), countryName));
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private List<RecommendCityInfo> convert(List<SeoHotCityinfoDO> cityList, List<SeoHotDestinatioinfoDO> destanationList, String locale) {
        Map<Integer, Long> cityOrderMap = seoService.getCityOrderNum(destanationList);
        List<SeoHotCityinfoDO> citys = cityList.stream().sorted(Comparator.comparing(l -> cityOrderMap.getOrDefault(l.getCityId(), 999L))).limit(tripConfig.getMaxRecommendCityCount()).toList();
        Map<Long, City> cityMap = seoService.getCity(citys.stream().map(l -> l.getCityId().longValue()).distinct().collect(Collectors.toList()), locale);
        return citys.stream().map(l -> {
            if (StringUtils.isEmpty(l.getImage())) {
                return null;
            }
            RecommendCityInfo item = new RecommendCityInfo();
            item.setCityName(cityMap.get(l.getCityId().longValue()).getTranslationName());
            item.setUrl(seoService.getSiteUrl(l.getUrl(), locale));
            item.setImgUrl(l.getImage());
            return item;
        }).filter(Objects::nonNull).limit(4).collect(Collectors.toList());
    }
}
