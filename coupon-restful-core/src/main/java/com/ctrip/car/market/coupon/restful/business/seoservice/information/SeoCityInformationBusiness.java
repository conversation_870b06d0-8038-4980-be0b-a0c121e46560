package com.ctrip.car.market.coupon.restful.business.seoservice.information;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.business.seoservice.vehicle.SeoCityVehicleBusiness;
import com.ctrip.car.market.coupon.restful.contract.seo.InformationInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationResponseType;
import com.ctrip.car.market.coupon.restful.enums.InformationEnum;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class SeoCityInformationBusiness extends SeoBaseService<QuerySeoInformationRequestType, QuerySeoInformationResponseType> {

    public SeoCityInformationBusiness() {
        super(Lists.newArrayList(SeoPage.COUNTRY, SeoPage.CITY, SeoPage.AIRPORT));
    }

    @Resource
    private SeoService service;

    @Resource
    private SeoCityVehicleBusiness vehicleBusiness;

    @Override
    public QuerySeoInformationResponseType doBusiness(QuerySeoInformationRequestType request, SeoPage seoPage) {
        return queryInformation(request);
    }

    public QuerySeoInformationResponseType queryInformation(QuerySeoInformationRequestType request) {
        QuerySeoInformationResponseType response = new QuerySeoInformationResponseType();
        response.setInformationList(Lists.newArrayList());
        if (!checkRequest(request)) {
            Metrics.build().withTag("errorType", "paraError").withTag("result", "0").recordOne(MetricsEnum.SEO_INFORMATION.getTitle());
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        SeoHotDestinatioinfoDO hotDestinationInfo = service.queryHotDestinationFirst(request.getCountryId(), request.getCityId(), request.getPoiType(), request.getPoiCode());
        if (hotDestinationInfo == null) {
            Metrics.build().withTag("errorType", "destinationError").withTag("result", "0").recordOne(MetricsEnum.SEO_INFORMATION.getTitle());
            response.setBaseResponse(ResponseUtil.fail("destination error"));
            return response;
        }
        response.setInformationList(getInformation(hotDestinationInfo, request));
        Metrics.build().withTag("country", Optional.ofNullable(hotDestinationInfo.getCountryId()).orElse(0).toString())
                .withTag("city", Optional.ofNullable(hotDestinationInfo.getCityId()).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getInformationList()) ? "1" : "0")
                .recordOne(MetricsEnum.SEO_INFORMATION.getTitle());
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private List<InformationInfo> getInformation(SeoHotDestinatioinfoDO hotDestinationInfo, QuerySeoInformationRequestType request) {
        List<InformationInfo> result = Lists.newArrayList();
        SeoHotInformationDO information = service.queryInformationByPoi(hotDestinationInfo.getPoiType(), hotDestinationInfo.getPoiCode());
        if (information == null) {
            return result;
        }
        String locale = request.getBaseRequest().getLocale();
        //热门供应商
        if (StringUtils.isNotEmpty(information.getVendorName())) {
            result.add(getInformation(InformationEnum.Vendor.getType(), information.getVendorName(), locale));
        }
        //热门车型组
        if (StringUtils.isNotEmpty(information.getVehicleGroupName()) && information.getVehicleGroupId() != null) {
            String vehicleGroupName = service.getVehicleGroupName(information.getVehicleGroupId().toString(), information.getVehicleGroupName(), locale);
            result.add(getInformation(InformationEnum.VehicleGroup.getType(), vehicleGroupName, locale));
            //每日均价
            if (Optional.ofNullable(information.getVehicleGroupId()).orElse(0) > 0) {
                BigDecimal price = getVehicleGroupPrice(hotDestinationInfo, request, information.getVehicleGroupId().toString());
                if (price != null) {
                    String priceStr = service.currencyString(price, locale, request.getBaseRequest().getCurrencyCode());
                    if (StringUtils.isNotEmpty(priceStr)) {
                        result.add(getInformation(InformationEnum.VehiclePrice.getType(), priceStr, locale));
                    }
                }
            }
        }
        //常见租期
        if (Optional.ofNullable(information.getTenancy()).orElse(0) > 0) {
            if (information.getTenancy() > 1) {
                result.add(getInformation(InformationEnum.Tenancy.getType(), LanguageUtils.sharkValFormat(SeoShark.HotRentalTermPluralValue.getValue(locale), information.getTenancy()), locale));
            } else {
                result.add(getInformation(InformationEnum.Tenancy.getType(), LanguageUtils.sharkValFormat(SeoShark.HotRentalTermValue.getValue(locale), information.getTenancy()), locale));
            }
        }

        return result;
    }

    private BigDecimal getVehicleGroupPrice(SeoHotDestinatioinfoDO hotDestinationInfo, QuerySeoInformationRequestType request, String vehicleGroupCode) {
        QueryRecomdProductsResponseType vehicleResponse = vehicleBusiness.queryVehicle(request.getBaseRequest(), request.getHead(), hotDestinationInfo, true);
        if (vehicleResponse != null && CollectionUtils.isNotEmpty(vehicleResponse.getRecomdProductResList()) && CollectionUtils.isNotEmpty(vehicleResponse.getRecomdProductResList().get(0).getProducts())) {
            List<RecomdProduct> vehicleList = vehicleResponse.getRecomdProductResList().get(0).getProducts().stream().filter(l -> l.getVehicle() != null && StringUtils.equalsIgnoreCase(l.getVehicle().getGroupCode(), vehicleGroupCode)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(vehicleList)) {
                return vehicleList.stream().map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
            }
        }
        return null;
    }

    private InformationInfo getInformation(int type, String name, String locale) {
        InformationInfo informationInfo = new InformationInfo();
        informationInfo.setType(type);
        informationInfo.setName(name);
        switch (type) {
            case 1:
                informationInfo.setTitle(SeoShark.HotCompany.getValue(locale));
                break;
            case 2:
                informationInfo.setTitle(SeoShark.HotVehicleGroup.getValue(locale));
                break;
            case 3:
                informationInfo.setTitle(SeoShark.HotDailyPrice.getValue(locale));
                break;
            case 4:
                informationInfo.setTitle(SeoShark.HotRentalTerm.getValue(locale));
                break;
        }
        return informationInfo;
    }

    private boolean checkRequest(QuerySeoInformationRequestType request) {
        if (request.getBaseRequest() == null) {
            return false;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) <= 0
                && Optional.ofNullable(request.getCountryId()).orElse(0) <= 0
                && StringUtils.isEmpty(request.getPoiCode())) {
            return false;
        }
        if (StringUtils.isEmpty(request.getBaseRequest().getRequestId())) {
            request.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        }
        return true;
    }
}
