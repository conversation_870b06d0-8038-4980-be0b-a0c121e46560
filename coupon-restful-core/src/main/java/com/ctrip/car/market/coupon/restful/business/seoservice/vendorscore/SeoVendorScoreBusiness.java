package com.ctrip.car.market.coupon.restful.business.seoservice.vendorscore;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.business.seoservice.SeoBaseService;
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache;
import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVendorScoreRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVendorScoreResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.VendorScoreItem;
import com.ctrip.car.market.coupon.restful.dto.CustomerConfigDTO;
import com.ctrip.car.market.coupon.restful.dto.SeoProvinceQueryParameter;
import com.ctrip.car.market.coupon.restful.dto.SubItemConfigDto;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import com.ctrip.car.osd.framework.common.utils.JsonUtil;
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.CommonQueryCommentSummaryResponseType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class SeoVendorScoreBusiness extends SeoBaseService<QuerySeoVendorScoreRequestType, QuerySeoVendorScoreResponseType> {

    public SeoVendorScoreBusiness() {
        super(Lists.newArrayList(SeoPage.TRIP_HOME, SeoPage.COUNTRY, SeoPage.PROVINCE, SeoPage.CITY, SeoPage.AIRPORT, SeoPage.STATION, SeoPage.SCENERY));
    }

    @Resource
    private SeoService seoService;

    @Resource
    private TripConfig tripConfig;

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private UcpServiceProxy ucpServiceProxy;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    @Override
    public QuerySeoVendorScoreResponseType doBusiness(QuerySeoVendorScoreRequestType request, SeoPage seoPage) {
        QuerySeoVendorScoreResponseType response = new QuerySeoVendorScoreResponseType();
        Integer countryId = request.getCountryId();
        Integer cityId = request.getCityId();
        if (Objects.equals(seoPage, SeoPage.PROVINCE) || Objects.equals(seoPage, SeoPage.STATION) || Objects.equals(seoPage, SeoPage.SCENERY)) {
            SeoProvinceQueryParameter queryParameter = seoService.buildQueryParameter(request.getProvinceId(), request.getPoiType(), request.getPoiCode());
            if (queryParameter != null) {
                countryId = queryParameter.getQueryParameter().getCountryId();
                cityId = queryParameter.getQueryParameter().getCityId();
            }
        }
        if (Objects.equals(seoPage, SeoPage.AIRPORT)) {
            SeoHotDestinatioinfoDO destinatioinfoDO = seoService.queryHotDestinationFirst(null, null, 1, request.getPoiCode());
            if (destinatioinfoDO != null) {
                countryId = destinatioinfoDO.getCountryId();
                cityId = destinatioinfoDO.getCityId();
            }
        }
        //热门供应商
        List<String> vendorList = getHotVendor(countryId, cityId);
        response.setVendorScoreList(getVendorScore(vendorList, request.getBaseRequest(), seoPage, cityId));
        return response;
    }

    private List<VendorScoreItem> getVendorScore(List<String> vendorList, BaseRequest baseRequest, SeoPage seoPage, Integer cityId) {
        return vendorList.stream().map(l -> {
            SeoHotVendorDO vendorDO = seoVendorCache.queryVendor(l);
            if (vendorDO == null) {
                return null;
            }
            VendorScoreItem item = new VendorScoreItem();
            //供应商logo
            item.setVendorCode(l);
            item.setVendorName(vendorDO.getVendorName());
            item.setVendorLogo(seoService.getVendorLogo(l));
            item.setUrl(seoService.getSiteUrl(vendorDO.getUrl(), baseRequest.getLocale()));
            //首页
            if (Objects.equals(seoPage, SeoPage.TRIP_HOME)) {
                //供应商总分
                SeoVendorCommentScoreDO vendorCommentScoreDO = seoVendorCache.queryVendorCommentScore(l);
                item.setScore(vendorCommentScoreDO == null ? null : vendorCommentScoreDO.getSocre());
                item.setCommentCount(vendorCommentScoreDO == null ? null : vendorCommentScoreDO.getTotalCount());
                List<SubItemConfigDto> subItemList = vendorCommentScoreDO == null ? null : JsonUtil.getObjList(vendorCommentScoreDO.getSubItemScore(), SubItemConfigDto.class);
                item.setSubScoreList(seoService.dbSubItemConvert(subItemList, baseRequest.getLocale()));
            } else {
                //供应商x城市维度
                CommonQueryCommentSummaryResponseType responseType = ucpServiceProxy.queryVendorCityComment(baseRequest.getLocale(), l, cityId);
                if (responseType.getCommentAggregation() != null) {
                    item.setScore(scoreFormat(responseType.getCommentAggregation().getScoreAvg()));
                    item.setCommentCount(Optional.ofNullable(responseType.getCommentAggregation().getTotalCount()).orElse(0));
                    item.setSubScoreList(seoService.apiSubItemConvert(responseType.getCommentAggregation().getSubItemTags(), baseRequest.getLocale()));
                }
            }
            item.setPrice(getVendorPrice(baseRequest, l));
            return item;
        }).filter(Objects::nonNull).limit(8).toList();
    }

    private String getVendorPrice(BaseRequest baseRequest, String vendorCode) {
        try {
            RecomdPriceDTO recomdPriceDTO = osdShoppingProxy.getVendorPrice(baseRequest, vendorCode);
            if (recomdPriceDTO == null) {
                return null;
            }
            return seoService.currencyString(recomdPriceDTO.getCurrentDailyPrice(), baseRequest.getLocale(), baseRequest.getCurrencyCode());
        } catch (Exception e) {
            return null;
        }
    }

    private List<String> getHotVendor(Integer countryId, Integer cityId) {
        if (tripConfig.getSeoHotVendor() == null) {
            return Lists.newArrayList();
        }
        //优先读取city
        CustomerConfigDTO customerConfig = Optional.ofNullable(tripConfig.getSeoHotVendor().getCustomerConfig()).orElse(Lists.newArrayList()).stream().filter(l -> Objects.equals(l.getType(), 2) && Objects.equals(l.getId(), cityId)).findFirst().orElse(null);
        if (customerConfig == null) {
            //读取国家
            customerConfig = Optional.ofNullable(tripConfig.getSeoHotVendor().getCustomerConfig()).orElse(Lists.newArrayList()).stream().filter(l -> Objects.equals(l.getType(), 1) && Objects.equals(l.getId(), countryId)).findFirst().orElse(null);
        }
        String vendorStr = customerConfig == null ? tripConfig.getSeoHotVendor().getDefaultConfig() : customerConfig.getVendorList();
        return Lists.newArrayList(vendorStr.split(","));
    }

    private BigDecimal scoreFormat(BigDecimal score) {
        return score == null ? null : score.setScale(1, RoundingMode.UP);
    }
}
