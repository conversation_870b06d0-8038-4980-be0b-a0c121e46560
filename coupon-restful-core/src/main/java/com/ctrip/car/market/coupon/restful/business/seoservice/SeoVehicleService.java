package com.ctrip.car.market.coupon.restful.business.seoservice;

import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListResponseType;
import com.ctrip.car.market.coupon.restful.enums.SeoPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SeoVehicleService {

    @Resource
    private List<ISeoService<QuerySeoVehicleListRequestType, QuerySeoVehicleListResponseType>> seoServiceList;

    public QuerySeoVehicleListResponseType queryVehicleList(QuerySeoVehicleListRequestType request) {
        SeoPage seoPage = SeoPage.getPage(request.getCountryId(), request.getProvinceId(), request.getCityId(), request.getPoiType(), request.getPoiCode(), request.getVendorCode());
        if (seoPage == null) {
            return new QuerySeoVehicleListResponseType();
        }
        ISeoService<QuerySeoVehicleListRequestType, QuerySeoVehicleListResponseType> service =
                seoServiceList.stream().filter(l -> l.isSupport(seoPage)).findFirst().orElse(null);
        if (service == null) {
            return new QuerySeoVehicleListResponseType();
        }
        return service.query(request, seoPage);
    }
}
