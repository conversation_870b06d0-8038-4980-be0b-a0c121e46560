<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>coupon-restful</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>0.0.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>coupon-restful-core</artifactId>
    <dependencies>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-restful-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.infosec</groupId>
            <artifactId>risk_realtime_wsclient</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour.ai</groupId>
            <artifactId>userlabelservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.car.sd.carosdshoppingserviceapi.v1</groupId>
            <artifactId>carosdshoppingserviceapi</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                    <artifactId>dto</artifactId>
                </exclusion>
            </exclusions>

        </dependency>
        <dependency>
            <groupId>com.ctrip.ibu.platform</groupId>
            <artifactId>ibu-shark-sdk</artifactId>
            <version>5.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.clogging</groupId>
            <artifactId>clogging-agent</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>vi</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>maiar-client</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>coupon-common</artifactId>
            <version>3.2.42</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.car.market</groupId>
                    <artifactId>framework-related</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.car</groupId>
                    <artifactId>maiar-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.di.data</groupId>
                    <artifactId>abtestclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-server</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>accounts-mobile-request-filter</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-extensions</artifactId>
        </dependency>


        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>mergecouponservice-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>soa-common</artifactId>
                    <groupId>com.ctrip.igt.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.ctrip.car</groupId>-->
        <!--            <artifactId>coupon-client</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>service-accountsquery-client</artifactId>
            <version>1.0.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.basebiz</groupId>
                    <artifactId>accounts-soa-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.market.v1</groupId>
            <artifactId>promocode-client-contract</artifactId>
        </dependency>


        <!--汽车票订单接口-->

        <!--<dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>distlock-client</artifactId>
        </dependency>-->


        <dependency>
            <groupId>com.ctrip.car.osd</groupId>
            <artifactId>osd-common-client</artifactId>
            <version>1.0.14</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.platform.members.membersinfojservice.v1</groupId>
            <artifactId>membersinfojservice</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>accounts-mobile-request-filter</artifactId>
                    <groupId>com.ctrip.basebiz</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>coreinfo-service-client</artifactId>
        </dependency>



        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-restful-utils</artifactId>
            <version>0.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.car.sd.api8961.v1</groupId>
            <artifactId>api8961</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.sd</groupId>
            <artifactId>dynamic-tp-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>transmittable-thread-local</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.infosec.kms</groupId>
            <artifactId>kms-sdk</artifactId>
            <version>1.0.4</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.34</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>framework-related</artifactId>
            <version>1.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>market-crossrecommend-contract</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.dcs.geo</groupId>
            <artifactId>geo-platform-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.car.osd.carosdbasicdataservice.v1</groupId>
            <artifactId>carosdbasicdataservice</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.osd.cartranslateservice</groupId>
            <artifactId>cartranslateservice</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dto</artifactId>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.foundation</groupId>
            <artifactId>common-translation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>metric-client</artifactId>
                    <groupId>com.ctrip.flight.intl.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-math3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ibu-shark-sdk</artifactId>
                    <groupId>com.ctrip.ibu.platform</groupId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.30408</groupId>
            <artifactId>car-commodity-vendor-query-service</artifactId>
            <version>1.0.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>soa-common</artifactId>
                    <groupId>com.ctrip.car.commodity.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.sd</groupId>
            <artifactId>carcache-all</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>compiler</artifactId>
                    <groupId>com.github.spullara.mustache.java</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dto</artifactId>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.annotation-api</artifactId>
                    <groupId>javax.annotation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.osd.framework</groupId>
            <artifactId>common</artifactId>
            <version>2.14.19</version>
            <exclusions>
                <exclusion>
                    <artifactId>dto</artifactId>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>transmittable-thread-local</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>metric-client</artifactId>
                    <groupId>com.ctrip.flight.intl.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ibu-gdpr-sdk</artifactId>
                    <groupId>com.ctrip.ibu.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.tour.tripservice</groupId>
            <artifactId>ucp-biz-systemservice-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
            <artifactId>geolocationservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.21394</groupId>
            <artifactId>seo-platform-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.igt.geo</groupId>
            <artifactId>geo-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.25057</groupId>
            <artifactId>carcommoditycommonservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.gs</groupId>
            <artifactId>global-poi-soa-client</artifactId>
        </dependency>

    </dependencies>

</project>
